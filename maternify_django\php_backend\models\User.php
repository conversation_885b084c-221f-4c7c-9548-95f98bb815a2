<?php
/**
 * User Model for MATERNIFY PHP Backend
 */

class User {
    private $conn;
    private $table_name = "users";

    public $id;
    public $username;
    public $email;
    public $password_hash;
    public $full_name;
    public $due_date;
    public $is_pregnant;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Create user
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET username=:username, email=:email, password_hash=:password_hash, 
                      full_name=:full_name, due_date=:due_date, is_pregnant=:is_pregnant";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->username = htmlspecialchars(strip_tags($this->username));
        $this->email = htmlspecialchars(strip_tags($this->email));
        $this->full_name = htmlspecialchars(strip_tags($this->full_name));

        // Bind values
        $stmt->bindParam(":username", $this->username);
        $stmt->bindParam(":email", $this->email);
        $stmt->bindParam(":password_hash", $this->password_hash);
        $stmt->bindParam(":full_name", $this->full_name);
        $stmt->bindParam(":due_date", $this->due_date);
        $stmt->bindParam(":is_pregnant", $this->is_pregnant);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Read user by ID
    public function readOne() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->username = $row['username'];
            $this->email = $row['email'];
            $this->password_hash = $row['password_hash'];
            $this->full_name = $row['full_name'];
            $this->due_date = $row['due_date'];
            $this->is_pregnant = $row['is_pregnant'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }

        return false;
    }

    // Find user by username or email
    public function findByCredentials($username_or_email) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE username = :credential OR email = :credential 
                  LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":credential", $username_or_email);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->id = $row['id'];
            $this->username = $row['username'];
            $this->email = $row['email'];
            $this->password_hash = $row['password_hash'];
            $this->full_name = $row['full_name'];
            $this->due_date = $row['due_date'];
            $this->is_pregnant = $row['is_pregnant'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }

        return false;
    }

    // Update user
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET full_name=:full_name, due_date=:due_date, is_pregnant=:is_pregnant 
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->full_name = htmlspecialchars(strip_tags($this->full_name));

        // Bind values
        $stmt->bindParam(":full_name", $this->full_name);
        $stmt->bindParam(":due_date", $this->due_date);
        $stmt->bindParam(":is_pregnant", $this->is_pregnant);
        $stmt->bindParam(":id", $this->id);

        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // Verify password
    public function verifyPassword($password) {
        return password_verify($password, $this->password_hash);
    }

    // Hash password
    public function hashPassword($password) {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    // Get user's pregnancy stats
    public function getPregnancyStats() {
        if (!$this->due_date || !$this->is_pregnant) {
            return null;
        }

        $due_date = new DateTime($this->due_date);
        $today = new DateTime();
        $pregnancy_start = clone $due_date;
        $pregnancy_start->sub(new DateInterval('P280D')); // 40 weeks = 280 days

        $days_to_go = $today->diff($due_date)->days;
        $days_since_start = $pregnancy_start->diff($today)->days;
        $current_week = max(0, floor($days_since_start / 7));

        if ($current_week <= 12) {
            $trimester = '1st';
        } elseif ($current_week <= 27) {
            $trimester = '2nd';
        } else {
            $trimester = '3rd';
        }

        return [
            'days_to_go' => max(0, $days_to_go),
            'current_week' => $current_week,
            'trimester' => $trimester,
            'due_date' => $this->due_date
        ];
    }

    // Get user's recent calculations
    public function getRecentCalculations($limit = 5) {
        $query = "SELECT * FROM health_calculations
                  WHERE user_id = :user_id
                  ORDER BY created_at DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $this->id);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();

        $calculations = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Decode JSON data
        foreach ($calculations as &$calc) {
            $calc['input_data'] = json_decode($calc['input_data'], true);
            $calc['result_data'] = json_decode($calc['result_data'], true);
        }

        return $calculations;
    }

    // Get user's forum posts count
    public function getForumPostsCount() {
        $query = "SELECT COUNT(*) as count FROM forum_posts WHERE user_id = :user_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'];
    }

    // Get user's upcoming appointments
    public function getUpcomingAppointments($limit = 3) {
        $query = "SELECT * FROM appointments 
                  WHERE user_id = :user_id 
                  AND appointment_date >= NOW() 
                  AND is_completed = FALSE 
                  ORDER BY appointment_date ASC 
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $this->id);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Convert to array for JSON response
    public function toArray() {
        return [
            'id' => $this->id,
            'username' => $this->username,
            'email' => $this->email,
            'full_name' => $this->full_name,
            'due_date' => $this->due_date,
            'is_pregnant' => $this->is_pregnant,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
?>
