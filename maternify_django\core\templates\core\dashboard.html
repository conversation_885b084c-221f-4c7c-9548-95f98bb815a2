{% extends 'base.html' %}
{% load static %}

{% block title %}Dashboard - MATERNIFY{% endblock %}

{% block content %}
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="{% static 'images/logo.svg' %}" alt="MATERNIFY Logo">
                <span>MATERNIFY</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="{% url 'core:dashboard' %}" class="nav-link active">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:pregnancy_care' %}" class="nav-link">Pregnancy Care</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:infant_care' %}" class="nav-link">Infant Care</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:tools' %}" class="nav-link">Tools</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:forum' %}" class="nav-link">Community Forum</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:resources' %}" class="nav-link">Resources</a>
                </li>
                <li class="nav-item">
                    <span class="nav-link user-greeting">Hi, {{ user.display_name }}!</span>
                </li>
                <li class="nav-item">
                    <a href="{% url 'accounts:logout' %}" class="nav-link logout-btn">Logout</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Welcome Section -->
            <section class="welcome-section">
                <div class="welcome-content">
                    <h1>Welcome back, {{ user.display_name }}!</h1>
                    <p>Here's your personalized healthcare dashboard</p>
                </div>
                <div class="welcome-stats">
                    {% if user.is_pregnant and user.due_date %}
                        <div class="stat-card">
                            <i class="fas fa-calendar-day"></i>
                            <div class="stat-info">
                                <span class="stat-number">{{ days_to_due|default:"--" }}</span>
                                <span class="stat-label">Days to go</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-baby"></i>
                            <div class="stat-info">
                                <span class="stat-number">{{ pregnancy_week|default:"--" }}</span>
                                <span class="stat-label">Current week</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-heartbeat"></i>
                            <div class="stat-info">
                                <span class="stat-number">{{ trimester|default:"--" }}</span>
                                <span class="stat-label">Trimester</span>
                            </div>
                        </div>
                    {% else %}
                        <div class="stat-card">
                            <i class="fas fa-user-plus"></i>
                            <div class="stat-info">
                                <span class="stat-number">New</span>
                                <span class="stat-label">Member</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-heart"></i>
                            <div class="stat-info">
                                <span class="stat-number">{{ recent_calculations.count }}</span>
                                <span class="stat-label">Calculations</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <i class="fas fa-calendar"></i>
                            <div class="stat-info">
                                <span class="stat-number">{{ upcoming_appointments.count }}</span>
                                <span class="stat-label">Appointments</span>
                            </div>
                        </div>
                    {% endif %}
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <h2>Health Tools</h2>
                <div class="actions-grid">
                    <a href="{% url 'core:tools' %}#due-date" class="action-card">
                        <i class="fas fa-calculator"></i>
                        <h3>Due Date Calculator</h3>
                        <p>Calculate your estimated due date</p>
                    </a>
                    <a href="{% url 'core:tools' %}#bmi" class="action-card">
                        <i class="fas fa-weight"></i>
                        <h3>BMI Calculator</h3>
                        <p>Check your body mass index</p>
                    </a>
                    <a href="{% url 'core:tools' %}#blood-pressure" class="action-card">
                        <i class="fas fa-heartbeat"></i>
                        <h3>Blood Pressure Tracker</h3>
                        <p>Monitor your blood pressure</p>
                    </a>
                    <a href="{% url 'core:tools' %}#nutrition" class="action-card">
                        <i class="fas fa-apple-alt"></i>
                        <h3>Nutrition Calculator</h3>
                        <p>Calculate daily nutrition needs</p>
                    </a>
                    <a href="{% url 'core:tools' %}#water-intake" class="action-card">
                        <i class="fas fa-tint"></i>
                        <h3>Water Intake Tracker</h3>
                        <p>Track your daily hydration</p>
                    </a>
                    <a href="{% url 'core:tools' %}#weight-gain" class="action-card">
                        <i class="fas fa-chart-line"></i>
                        <h3>Weight Gain Calculator</h3>
                        <p>Pregnancy weight gain guidance</p>
                    </a>
                </div>
            </section>

            <!-- Care & Community -->
            <section class="quick-actions">
                <h2>Care & Community</h2>
                <div class="actions-grid">
                    <a href="{% url 'core:pregnancy_care' %}" class="action-card">
                        <i class="fas fa-book-medical"></i>
                        <h3>Pregnancy Guide</h3>
                        <p>Trimester-wise care guidelines</p>
                    </a>
                    <a href="{% url 'core:infant_care' %}" class="action-card">
                        <i class="fas fa-baby-carriage"></i>
                        <h3>Infant Care</h3>
                        <p>Newborn care essentials</p>
                    </a>
                    <a href="{% url 'core:forum' %}" class="action-card">
                        <i class="fas fa-comments"></i>
                        <h3>Community Forum</h3>
                        <p>Connect with other mothers</p>
                    </a>
                    <a href="{% url 'core:resources' %}" class="action-card">
                        <i class="fas fa-video"></i>
                        <h3>Educational Resources</h3>
                        <p>Expert guidance and tips</p>
                    </a>
                </div>
            </section>

            <!-- Today's Tips -->
            <section class="tips-section">
                <h2>Today's Tips</h2>
                <div class="tips-container">
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-apple-alt"></i>
                        </div>
                        <div class="tip-content">
                            <h3>Nutrition Tip</h3>
                            <p id="nutritionTip">{{ daily_tips.nutrition|default:"Include plenty of leafy greens in your diet for essential folate and iron." }}</p>
                        </div>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-dumbbell"></i>
                        </div>
                        <div class="tip-content">
                            <h3>Exercise Tip</h3>
                            <p id="exerciseTip">{{ daily_tips.exercise|default:"Take a 20-30 minute walk daily to improve circulation and reduce swelling." }}</p>
                        </div>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-moon"></i>
                        </div>
                        <div class="tip-content">
                            <h3>Wellness Tip</h3>
                            <p id="wellnessTip">{{ daily_tips.wellness|default:"Practice deep breathing exercises to reduce stress and promote relaxation." }}</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recent Activity -->
            {% if recent_activity %}
            <section class="activity-section">
                <h2>Recent Activity</h2>
                <div class="activity-list">
                    {% for activity in recent_activity %}
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="{{ activity.icon }}"></i>
                        </div>
                        <div class="activity-content">
                            <h4>{{ activity.title }}</h4>
                            <p>{{ activity.description }}</p>
                            <span class="activity-time">{{ activity.time|timesince }} ago</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}

            <!-- Upcoming Reminders -->
            {% if upcoming_reminders %}
            <section class="reminders-section">
                <h2>Upcoming Reminders</h2>
                <div class="reminders-list">
                    {% for reminder in upcoming_reminders %}
                    <div class="reminder-item">
                        <div class="reminder-date">
                            <span class="day">{{ reminder.date.day }}</span>
                            <span class="month">{{ reminder.date|date:"M" }}</span>
                        </div>
                        <div class="reminder-content">
                            <h4>{{ reminder.title }}</h4>
                            <p>{{ reminder.description }}</p>
                            <span class="reminder-time">{{ reminder.time }}</span>
                        </div>
                        <div class="reminder-action">
                            <button class="btn-small">{{ reminder.type|title }}</button>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}

            <!-- Recent Calculations -->
            {% if recent_calculations %}
            <section class="activity-section">
                <h2>Recent Calculations</h2>
                <div class="activity-list">
                    {% for calculation in recent_calculations %}
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div class="activity-content">
                            <h4>{{ calculation.get_calculation_type_display }}</h4>
                            <p>Calculation completed successfully</p>
                            <span class="activity-time">{{ calculation.created_at|timesince }} ago</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}

            <!-- Recent Forum Posts -->
            {% if recent_posts %}
            <section class="activity-section">
                <h2>Recent Community Posts</h2>
                <div class="activity-list">
                    {% for post in recent_posts %}
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <div class="activity-content">
                            <h4><a href="{% url 'core:forum_detail' post.pk %}">{{ post.title }}</a></h4>
                            <p>by {{ post.author.display_name }}</p>
                            <span class="activity-time">{{ post.created_at|timesince }} ago</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="{% static 'images/logo.svg' %}" alt="MATERNIFY Logo">
                        <span>MATERNIFY</span>
                    </div>
                    <p>Empowering mothers with knowledge and support throughout their journey.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="{% url 'core:dashboard' %}">Dashboard</a></li>
                        <li><a href="{% url 'core:pregnancy_care' %}">Pregnancy Care</a></li>
                        <li><a href="{% url 'core:infant_care' %}">Infant Care</a></li>
                        <li><a href="{% url 'core:tools' %}">Tools</a></li>
                        <li><a href="{% url 'core:forum' %}">Community Forum</a></li>
                        <li><a href="{% url 'core:resources' %}">Resources</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 MATERNIFY. All rights reserved.</p>
            </div>
        </div>
    </footer>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/auth.js' %}"></script>
<script src="{% static 'js/dashboard-integration.js' %}"></script>
{% endblock %}
