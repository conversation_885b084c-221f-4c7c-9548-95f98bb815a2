{% extends 'base.html' %}
{% load static %}

{% block title %}Login - MATERNIFY{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/auth.css' %}">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="auth-container">
    <a href="{% url 'core:index' %}" class="back-to-home">
        <i class="fas fa-arrow-left"></i>
        Back to Home
    </a>

    <div class="auth-card">
        <div class="auth-logo">
            <img src="{% static 'images/logo.svg' %}" alt="MATERNIFY Logo">
            <span>MATERNIFY</span>
        </div>

        <h1 class="auth-title">Welcome Back</h1>
        <p class="auth-subtitle">Sign in to continue your journey</p>

        {% if messages %}
            <div id="alert-container">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        <span>{{ message }}</span>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <form class="auth-form" method="post">
            {% csrf_token %}
            <div class="form-group">
                <label for="username" class="form-label">Username</label>
                <input
                    type="text"
                    id="username"
                    name="username"
                    class="form-input"
                    placeholder="Enter your username"
                    required
                >
            </div>

            <div class="form-group">
                <label for="password" class="form-label">Password</label>
                <div class="password-container">
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input"
                        placeholder="Enter your password"
                        required
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                    </button>
                </div>
            </div>

            <button type="submit" class="auth-button">
                Sign In
            </button>
        </form>

        <div class="auth-divider">
            <span>or</span>
        </div>

        <div class="auth-link">
            <p>Don't have an account? <a href="{% url 'accounts:register' %}">Create one here</a></p>
        </div>

        <div class="auth-link">
            <p><a href="{% url 'accounts:password_reset' %}">Forgot your password?</a></p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/main.js' %}"></script>
<script src="{% static 'js/auth.js' %}"></script>
<script>
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(fieldId + 'ToggleIcon');

        if (field.type === 'password') {
            field.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            field.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }
</script>
{% endblock %}
