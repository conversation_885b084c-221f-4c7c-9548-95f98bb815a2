from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone


class CustomUser(AbstractUser):
    """Extended User model for MATERNIFY"""
    full_name = models.CharField(max_length=255, blank=True)
    due_date = models.DateField(null=True, blank=True)
    phone_number = models.CharField(max_length=20, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    emergency_contact = models.CharField(max_length=255, blank=True)
    emergency_phone = models.CharField(max_length=20, blank=True)
    profile_picture = models.ImageField(upload_to='profile_pics/', null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Pregnancy related fields
    is_pregnant = models.BooleanField(default=False)
    pregnancy_week = models.IntegerField(null=True, blank=True)
    pre_pregnancy_weight = models.FloatField(null=True, blank=True)
    current_weight = models.FloatField(null=True, blank=True)
    height = models.FloatField(null=True, blank=True)
    
    # Preferences
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)
    
    def __str__(self):
        return self.username
    
    @property
    def display_name(self):
        return self.full_name if self.full_name else self.username
    
    @property
    def current_pregnancy_week(self):
        if self.due_date and self.is_pregnant:
            from datetime import date
            today = date.today()
            days_since_lmp = (self.due_date - today).days
            weeks = 40 - (days_since_lmp // 7)
            return max(0, weeks)
        return None


class PregnancyRecord(models.Model):
    """Track pregnancy milestones and records"""
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='pregnancy_records')
    date_recorded = models.DateTimeField(default=timezone.now)
    weight = models.FloatField(null=True, blank=True)
    blood_pressure_systolic = models.IntegerField(null=True, blank=True)
    blood_pressure_diastolic = models.IntegerField(null=True, blank=True)
    notes = models.TextField(blank=True)
    doctor_visit = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['-date_recorded']
    
    def __str__(self):
        return f"{self.user.username} - {self.date_recorded.strftime('%Y-%m-%d')}"


class HealthCalculation(models.Model):
    """Store health calculation results"""
    CALCULATION_TYPES = [
        ('bmi', 'BMI Calculator'),
        ('due_date', 'Due Date Calculator'),
        ('weight_gain', 'Weight Gain Calculator'),
    ]
    
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE, related_name='calculations')
    calculation_type = models.CharField(max_length=20, choices=CALCULATION_TYPES)
    input_data = models.JSONField()  # Store input parameters
    result_data = models.JSONField()  # Store calculation results
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.user.username} - {self.get_calculation_type_display()}"
