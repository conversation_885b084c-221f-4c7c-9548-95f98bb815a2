{% extends 'base.html' %}
{% load static %}

{% block title %}About Us - MATERNIFY{% endblock %}

{% block content %}
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="{% static 'images/logo.svg' %}" alt="MATERNIFY Logo">
                <span>MATERNIFY</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="{% url 'core:index' %}" class="nav-link">Home</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:about' %}" class="nav-link active">About</a>
                </li>
                {% if user.is_authenticated %}
                    <li class="nav-item">
                        <a href="{% url 'core:dashboard' %}" class="nav-link">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'accounts:logout' %}" class="nav-link">Logout</a>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a href="{% url 'accounts:login' %}" class="nav-link login-btn">Login</a>
                    </li>
                    <li class="nav-item">
                        <a href="{% url 'accounts:register' %}" class="nav-link register-btn">Register</a>
                    </li>
                {% endif %}
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- About Hero Section -->
    <section class="about-hero">
        <div class="container">
            <div class="about-hero-content">
                <h1>About MATERNIFY</h1>
                <p>Empowering mothers and families with comprehensive healthcare guidance throughout the beautiful journey of pregnancy and early parenthood.</p>
            </div>
        </div>
    </section>

    <!-- Mission Section -->
    <section class="mission">
        <div class="container">
            <div class="mission-content">
                <div class="mission-text">
                    <h2>Our Mission</h2>
                    <p>At MATERNIFY, we believe every mother deserves access to reliable, evidence-based healthcare information and a supportive community. Our mission is to provide comprehensive resources, tools, and guidance to help mothers navigate their pregnancy journey and early parenthood with confidence.</p>
                    <p>We strive to bridge the gap between medical expertise and everyday maternal needs, offering personalized care recommendations, interactive tools, and a platform for mothers to connect and support each other.</p>
                </div>
                <div class="mission-image">
                    <i class="fas fa-hands-helping" style="font-size: 150px; color: #ff6b9d; opacity: 0.8;"></i>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services">
        <div class="container">
            <h2>Our Services</h2>
            <div class="services-grid">
                <div class="service-item">
                    <div class="service-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3>Pregnancy Tracking</h3>
                    <p>Comprehensive trimester-wise guidance covering nutrition, exercise, symptom management, and prenatal care recommendations.</p>
                </div>
                <div class="service-item">
                    <div class="service-icon">
                        <i class="fas fa-baby"></i>
                    </div>
                    <h3>Infant Care Guidance</h3>
                    <p>Essential newborn care information, vaccination schedules, growth tracking, and developmental milestone guidance.</p>
                </div>
                <div class="service-item">
                    <div class="service-icon">
                        <i class="fas fa-calculator"></i>
                    </div>
                    <h3>Health Calculators</h3>
                    <p>Interactive tools including due date calculators, BMI calculators, and other essential health metrics for mothers and babies.</p>
                </div>
                <div class="service-item">
                    <div class="service-icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3>Community Forum</h3>
                    <p>A safe space for mothers to share experiences, ask questions, and receive support from other mothers and healthcare professionals.</p>
                </div>
                <div class="service-item">
                    <div class="service-icon">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3>Educational Resources</h3>
                    <p>Curated collection of expert videos, articles, and guides covering all aspects of pregnancy and infant care.</p>
                </div>
                <div class="service-item">
                    <div class="service-icon">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <h3>Expert Guidance</h3>
                    <p>Evidence-based information reviewed by healthcare professionals, ensuring accuracy and reliability of all content.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Values Section -->
    <section class="values">
        <div class="container">
            <h2>Our Values</h2>
            <div class="values-grid">
                <div class="value-card">
                    <i class="fas fa-heart"></i>
                    <h3>Compassion</h3>
                    <p>We understand the emotional journey of motherhood and provide support with empathy and care.</p>
                </div>
                <div class="value-card">
                    <i class="fas fa-shield-alt"></i>
                    <h3>Trust</h3>
                    <p>All our information is evidence-based and reviewed by healthcare professionals for accuracy and safety.</p>
                </div>
                <div class="value-card">
                    <i class="fas fa-users"></i>
                    <h3>Community</h3>
                    <p>We foster a supportive environment where mothers can connect, share, and learn from each other.</p>
                </div>
                <div class="value-card">
                    <i class="fas fa-lightbulb"></i>
                    <h3>Innovation</h3>
                    <p>We continuously improve our platform with new tools and features to better serve our community.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="statistics">
        <div class="container">
            <h2>Our Impact</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <i class="fas fa-users"></i>
                    <div class="stat-number">{{ total_users }}+</div>
                    <div class="stat-label">Registered Users</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-calculator"></i>
                    <div class="stat-number">{{ total_calculations }}+</div>
                    <div class="stat-label">Health Calculations</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-comments"></i>
                    <div class="stat-number">{{ total_forum_posts }}+</div>
                    <div class="stat-label">Community Posts</div>
                </div>
                <div class="stat-card">
                    <i class="fas fa-book"></i>
                    <div class="stat-number">{{ total_articles }}+</div>
                    <div class="stat-label">Expert Articles</div>
                </div>
            </div>
        </div>
    </section>

    {% if team_members %}
    <!-- Team Section -->
    <section class="team">
        <div class="container">
            <h2>Meet Our Team</h2>
            <div class="team-grid">
                {% for member in team_members %}
                <div class="team-card">
                    {% if member.image %}
                        <img src="{{ member.image.url }}" alt="{{ member.name }}" class="team-image">
                    {% else %}
                        <div class="team-placeholder">
                            <i class="fas fa-user-md"></i>
                        </div>
                    {% endif %}
                    <div class="team-info">
                        <h3>{{ member.name }}</h3>
                        <p class="team-role">{{ member.role }}</p>
                        <p class="team-specialization">{{ member.specialization }}</p>
                        <p class="team-bio">{{ member.bio }}</p>
                        {% if member.years_experience %}
                            <p class="team-experience">{{ member.years_experience }}+ years experience</p>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>
    {% endif %}

    {% if featured_testimonials %}
    <!-- Testimonials Section -->
    <section class="testimonials">
        <div class="container">
            <h2>What Our Users Say</h2>
            <div class="testimonials-grid">
                {% for testimonial in featured_testimonials %}
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"{{ testimonial.content }}"</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-info">
                            <h4>{{ testimonial.user.get_full_name|default:testimonial.user.username }}</h4>
                            {% if testimonial.user_title %}
                                <p>{{ testimonial.user_title }}</p>
                            {% endif %}
                            {% if testimonial.user_location %}
                                <p>{{ testimonial.user_location }}</p>
                            {% endif %}
                        </div>
                        <div class="testimonial-rating">
                            {% for i in "12345" %}
                                {% if forloop.counter <= testimonial.rating %}
                                    <i class="fas fa-star"></i>
                                {% else %}
                                    <i class="far fa-star"></i>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>
    {% endif %}

    <!-- Contact Section -->
    <section class="contact">
        <div class="container">
            <h2>Get In Touch</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <h3>Contact Information</h3>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+****************</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-clock"></i>
                        <span>Monday - Friday: 9:00 AM - 6:00 PM EST</span>
                    </div>
                </div>
                <div class="contact-form-container">
                    <form id="contactForm" class="contact-form">
                        {% csrf_token %}
                        <div class="form-row">
                            <div class="form-group">
                                <label for="contactName">Name</label>
                                <input type="text" id="contactName" name="name" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label for="contactEmail">Email</label>
                                <input type="email" id="contactEmail" name="email" class="form-input" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="contactSubject">Subject</label>
                                <input type="text" id="contactSubject" name="subject" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label for="inquiryType">Inquiry Type</label>
                                <select id="inquiryType" name="inquiry_type" class="form-input">
                                    <option value="general">General Inquiry</option>
                                    <option value="support">Technical Support</option>
                                    <option value="partnership">Partnership</option>
                                    <option value="feedback">Feedback</option>
                                    <option value="media">Media Inquiry</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="contactMessage">Message</label>
                            <textarea id="contactMessage" name="message" class="form-input" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta">
        <div class="container">
            <div class="cta-content">
                <h2>Join Our Community Today</h2>
                <p>Start your journey with MATERNIFY and connect with thousands of mothers worldwide.</p>
                <div class="cta-buttons">
                    {% if user.is_authenticated %}
                        <a href="{% url 'core:dashboard' %}" class="btn btn-primary">Go to Dashboard</a>
                        <a href="{% url 'core:tools' %}" class="btn btn-outline">Health Tools</a>
                    {% else %}
                        <a href="{% url 'accounts:register' %}" class="btn btn-primary">Get Started</a>
                        <a href="{% url 'accounts:login' %}" class="btn btn-outline">Sign In</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <img src="{% static 'images/logo.svg' %}" alt="MATERNIFY Logo">
                        <span>MATERNIFY</span>
                    </div>
                    <p>Empowering mothers with knowledge and support throughout their journey.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="{% url 'core:index' %}">Home</a></li>
                        <li><a href="{% url 'core:about' %}">About Us</a></li>
                        {% if user.is_authenticated %}
                            <li><a href="{% url 'core:dashboard' %}">Dashboard</a></li>
                            <li><a href="{% url 'accounts:logout' %}">Logout</a></li>
                        {% else %}
                            <li><a href="{% url 'accounts:login' %}">Login</a></li>
                            <li><a href="{% url 'accounts:register' %}">Register</a></li>
                        {% endif %}
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 MATERNIFY. All rights reserved.</p>
            </div>
        </div>
    </footer>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/about.js' %}"></script>
{% endblock %}
