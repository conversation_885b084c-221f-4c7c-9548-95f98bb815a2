// Health Tools JavaScript functionality

document.addEventListener('DOMContentLoaded', function() {
    console.log('Tools page loaded, initializing...');
    initializeTools();
});

function initializeTools() {
    console.log('Initializing tools...');

    // Update user greeting
    const user = getCurrentUser();
    if (user) {
        const greetingElement = document.getElementById('userGreeting');
        if (greetingElement) {
            greetingElement.textContent = `Hi, ${user.name || user.fullName || 'User'}!`;
        }
    }

    // Initialize form handlers
    console.log('Initializing calculators...');
    initializeDueDateCalculator();
    initializeBMICalculator();
    initializeWeightGainCalculator();
    console.log('Tools initialization complete');
}

// Due Date Calculator
function initializeDueDateCalculator() {
    console.log('Initializing Due Date Calculator...');
    const form = document.getElementById('dueDateForm');
    if (!form) {
        console.error('Due Date form not found!');
        return;
    }

    console.log('Due Date form found, adding event listener');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('Due Date form submitted');
        calculateDueDate();
    });
}

function calculateDueDate() {
    const lmpDate = document.getElementById('lmpDate').value;

    if (!lmpDate) {
        showAlert('Please enter your last menstrual period date.', 'error');
        return;
    }

    const lmp = new Date(lmpDate);
    const today = new Date();

    // Validate LMP date
    if (lmp > today) {
        showAlert('Last menstrual period date cannot be in the future.', 'error');
        return;
    }

    // Validate reasonable date range (not more than 10 months ago)
    const tenMonthsAgo = new Date();
    tenMonthsAgo.setMonth(tenMonthsAgo.getMonth() - 10);
    if (lmp < tenMonthsAgo) {
        showAlert('Please enter a more recent date. If your pregnancy is longer than 10 months, please consult your healthcare provider.', 'warning');
        return;
    }

    // Calculate due date (280 days from LMP)
    const dueDate = new Date(lmp);
    dueDate.setDate(dueDate.getDate() + 280);

    // Calculate current pregnancy week
    const daysSinceLMP = Math.floor((today - lmp) / (1000 * 60 * 60 * 24));
    const currentWeek = Math.floor(daysSinceLMP / 7);

    // Calculate days remaining
    const daysRemaining = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

    // Determine trimester
    let trimester;
    if (currentWeek <= 12) {
        trimester = 'First Trimester';
    } else if (currentWeek <= 27) {
        trimester = 'Second Trimester';
    } else {
        trimester = 'Third Trimester';
    }

    // Display results
    document.getElementById('calculatedDueDate').textContent = formatDate(dueDate);
    document.getElementById('currentPregnancyWeek').textContent = `Week ${currentWeek}`;
    document.getElementById('daysRemaining').textContent = daysRemaining > 0 ? `${daysRemaining} days` : 'Due now!';
    document.getElementById('currentTrimester').textContent = trimester;

    // Show result
    document.getElementById('dueDateResult').style.display = 'block';

    // Scroll to result smoothly
    setTimeout(() => {
        document.getElementById('dueDateResult').scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 100);

    // Save to user profile if logged in
    const user = getCurrentUser();
    if (user) {
        updateUserProfile({ dueDate: dueDate.toISOString().split('T')[0] });
    }

    // Save calculation result
    saveCalculationResult('dueDate', {
        lmpDate: lmpDate,
        dueDate: dueDate.toISOString().split('T')[0],
        currentWeek: currentWeek,
        trimester: trimester
    });
}

// BMI Calculator
function initializeBMICalculator() {
    const form = document.getElementById('bmiForm');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        calculateBMI();
    });
}

function calculateBMI() {
    const height = parseFloat(document.getElementById('height').value);
    const weight = parseFloat(document.getElementById('weight').value);
    const heightUnit = document.getElementById('heightUnit').value;
    const weightUnit = document.getElementById('weightUnit').value;

    if (!height || !weight) {
        showAlert('Please enter both height and weight.', 'error');
        return;
    }

    if (height <= 0 || weight <= 0) {
        showAlert('Please enter valid positive values for height and weight.', 'error');
        return;
    }

    // Convert to metric units
    let heightInMeters = height;
    let weightInKg = weight;

    if (heightUnit === 'ft') {
        heightInMeters = height * 0.3048; // feet to meters
    } else {
        heightInMeters = height / 100; // cm to meters
    }

    if (weightUnit === 'lbs') {
        weightInKg = weight * 0.453592; // pounds to kg
    }

    // Calculate BMI
    const bmi = weightInKg / (heightInMeters * heightInMeters);

    // Determine category
    let category, categoryClass;
    if (bmi < 18.5) {
        category = 'Underweight';
        categoryClass = 'underweight';
    } else if (bmi < 25) {
        category = 'Normal Weight';
        categoryClass = 'normal';
    } else if (bmi < 30) {
        category = 'Overweight';
        categoryClass = 'overweight';
    } else {
        category = 'Obese';
        categoryClass = 'obese';
    }

    // Display results
    document.getElementById('calculatedBMI').textContent = bmi.toFixed(1);
    const categoryElement = document.getElementById('bmiCategory');
    categoryElement.textContent = category;
    categoryElement.className = `result-category ${categoryClass}`;

    // Highlight the appropriate BMI range
    document.querySelectorAll('.bmi-range').forEach(range => {
        range.classList.remove('active');
    });
    document.querySelector(`.bmi-range.${categoryClass}`).classList.add('active');

    // Show result
    document.getElementById('bmiResult').style.display = 'block';

    // Scroll to result smoothly
    setTimeout(() => {
        document.getElementById('bmiResult').scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 100);

    // Save calculation result
    saveCalculationResult('bmi', {
        height: height,
        weight: weight,
        heightUnit: heightUnit,
        weightUnit: weightUnit,
        bmi: bmi.toFixed(1),
        category: category
    });
}

// Weight Gain Calculator
function initializeWeightGainCalculator() {
    const form = document.getElementById('weightGainForm');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        calculateWeightGain();
    });
}

function calculateWeightGain() {
    const weight = parseFloat(document.getElementById('prePregnancyWeight').value);
    const height = parseFloat(document.getElementById('pregnancyHeight').value);
    const pregnancyType = document.getElementById('pregnancyType').value;

    if (!weight || !height || !pregnancyType) {
        showAlert('Please fill in all fields.', 'error');
        return;
    }

    if (weight <= 0 || height <= 0) {
        showAlert('Please enter valid positive values for weight and height.', 'error');
        return;
    }

    // Calculate pre-pregnancy BMI
    const heightInMeters = height / 100;
    const bmi = weight / (heightInMeters * heightInMeters);

    // Determine BMI category
    let category;
    if (bmi < 18.5) {
        category = 'Underweight';
    } else if (bmi < 25) {
        category = 'Normal Weight';
    } else if (bmi < 30) {
        category = 'Overweight';
    } else {
        category = 'Obese';
    }

    // Weight gain recommendations (in kg)
    let weightGainRange;

    if (pregnancyType === 'single') {
        if (bmi < 18.5) {
            weightGainRange = '12.5 - 18 kg';
        } else if (bmi < 25) {
            weightGainRange = '11.5 - 16 kg';
        } else if (bmi < 30) {
            weightGainRange = '7 - 11.5 kg';
        } else {
            weightGainRange = '5 - 9 kg';
        }
    } else if (pregnancyType === 'twins') {
        if (bmi < 18.5) {
            weightGainRange = '22.5 - 28 kg';
        } else if (bmi < 25) {
            weightGainRange = '16.5 - 24.5 kg';
        } else if (bmi < 30) {
            weightGainRange = '14 - 22.5 kg';
        } else {
            weightGainRange = '11 - 19 kg';
        }
    } else {
        weightGainRange = 'Consult your healthcare provider for personalized recommendations';
    }

    // Display results
    document.getElementById('weightGainRange').textContent = weightGainRange;
    document.getElementById('prePregnancyBMI').textContent = bmi.toFixed(1);
    document.getElementById('prePregnancyCategory').textContent = category;

    // Show result
    document.getElementById('weightGainResult').style.display = 'block';

    // Scroll to result smoothly
    setTimeout(() => {
        document.getElementById('weightGainResult').scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 100);

    // Save calculation result
    saveCalculationResult('weightGain', {
        prePregnancyWeight: weight,
        height: height,
        pregnancyType: pregnancyType,
        bmi: bmi.toFixed(1),
        category: category,
        recommendedGain: weightGainRange
    });
}

// Utility functions
function formatDate(date) {
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    return date.toLocaleDateString('en-US', options);
}

// Add smooth scrolling to tool sections
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a hash in the URL and scroll to it
    if (window.location.hash) {
        const target = document.querySelector(window.location.hash);
        if (target) {
            setTimeout(() => {
                target.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }, 100);
        }
    }
});

// Alert function for better user feedback
function showAlert(message, type = 'info') {
    // Create alert element
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = `
        <div class="alert-content">
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="alert-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
        </div>
    `;

    // Add to page
    document.body.appendChild(alert);

    // Remove after 5 seconds
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}

// Save calculation results to localStorage
function saveCalculationResult(type, data) {
    const user = getCurrentUser();
    if (!user) return;

    const calculations = JSON.parse(localStorage.getItem('userCalculations') || '[]');

    const calculation = {
        type: type,
        data: data,
        timestamp: new Date().toISOString(),
        userId: user.email
    };

    calculations.push(calculation);

    // Keep only last 50 calculations
    if (calculations.length > 50) {
        calculations.splice(0, calculations.length - 50);
    }

    localStorage.setItem('userCalculations', JSON.stringify(calculations));
}

// Get user's calculation history
function getUserCalculationHistory(type = null) {
    const user = getCurrentUser();
    if (!user) return [];

    const calculations = JSON.parse(localStorage.getItem('userCalculations') || '[]');

    let userCalculations = calculations.filter(calc => calc.userId === user.email);

    if (type) {
        userCalculations = userCalculations.filter(calc => calc.type === type);
    }

    return userCalculations.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}

// Export functions for global use
window.toolsFunctions = {
    calculateDueDate,
    calculateBMI,
    calculateWeightGain,
    formatDate,
    showAlert,
    saveCalculationResult,
    getUserCalculationHistory
};
