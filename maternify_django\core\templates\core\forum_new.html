{% extends 'core/base.html' %}
{% load static %}

{% block title %}Community Forum - MATERNIFY{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/dashboard.css' %}">
<style>
/* Forum-specific styles */
.forum-categories {
    margin-bottom: 2rem;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.category-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 2px solid transparent;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-color: #3498db;
}

.category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.category-icon i {
    font-size: 1.5rem;
    color: white;
}

.category-card h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.category-card p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.category-stats {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #888;
}

.category-stats span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.posts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.posts-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.posts-filter select {
    padding: 0.5rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    font-size: 1rem;
}

.posts-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.post-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.post-card:hover {
    transform: translateY(-2px);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.post-title {
    color: #2c3e50;
    text-decoration: none;
    font-size: 1.2rem;
    font-weight: 600;
}

.post-title:hover {
    color: #3498db;
}

.post-category {
    background: #3498db;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    white-space: nowrap;
}

.post-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.post-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.post-excerpt {
    color: #555;
    line-height: 1.6;
}

.post-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.post-action {
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.post-action:hover {
    color: #3498db;
}

.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: #f8f9fa;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

.original-post {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border-left: 4px solid #3498db;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .posts-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .posts-title {
        justify-content: space-between;
    }
    
    .post-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .modal-content {
        width: 95%;
        margin: 2% auto;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Navigation Bar -->
<nav class="navbar">
    <div class="nav-container">
        <div class="nav-logo">
            <i class="fas fa-baby"></i>
            <span>MATERNIFY</span>
        </div>
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="{% url 'core:dashboard' %}" class="nav-link">Dashboard</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">Pregnancy Care</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">Infant Care</a>
            </li>
            <li class="nav-item">
                <a href="{% url 'core:tools' %}" class="nav-link">Tools</a>
            </li>
            <li class="nav-item">
                <a href="{% url 'core:forum' %}" class="nav-link active">Forum</a>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link">Resources</a>
            </li>
            <li class="nav-item">
                <span class="nav-link user-greeting" id="userGreeting">Hi, {{ user.get_full_name|default:user.username }}!</span>
            </li>
            <li class="nav-item">
                <a href="{% url 'accounts:logout' %}" class="nav-link logout-btn">Logout</a>
            </li>
        </ul>
        <div class="hamburger">
            <span class="bar"></span>
            <span class="bar"></span>
            <span class="bar"></span>
        </div>
    </div>
</nav>

<!-- Forum Content -->
<main class="dashboard-main">
    <div class="container">
        <!-- Page Header -->
        <section class="page-header">
            <h1>Community Forum</h1>
            <p>Connect with other mothers, share experiences, and get support from our caring community</p>
        </section>

        <!-- Forum Categories -->
        <section class="forum-categories">
            <div class="categories-grid">
                {% for category_code, category_name in categories %}
                <div class="category-card" onclick="showCategory('{{ category_code }}')">
                    <div class="category-icon">
                        <i class="fas fa-{% if category_code == 'pregnancy' %}baby{% elif category_code == 'newborn' %}baby-carriage{% elif category_code == 'feeding' %}utensils{% elif category_code == 'health' %}heartbeat{% elif category_code == 'support' %}hands-helping{% else %}comments{% endif %}"></i>
                    </div>
                    <h3>{{ category_name }}</h3>
                    <p>
                        {% if category_code == 'pregnancy' %}Share your pregnancy experiences, ask questions, and get support
                        {% elif category_code == 'newborn' %}Tips, advice, and support for caring for your newborn baby
                        {% elif category_code == 'feeding' %}Breastfeeding, bottle feeding, and nutrition discussions
                        {% elif category_code == 'health' %}Health concerns, wellness tips, and medical advice
                        {% elif category_code == 'support' %}Share feelings, get emotional support, and connect with others
                        {% else %}General topics, introductions, and casual conversations
                        {% endif %}
                    </p>
                    <div class="category-stats">
                        <span><i class="fas fa-comments"></i> {{ category_stats|default_if_none:category_code.post_count|default:0 }} Posts</span>
                        <span><i class="fas fa-users"></i> {{ category_stats|default_if_none:category_code.member_count|default:0 }} Members</span>
                    </div>
                </div>
                {% endfor %}
            </div>
        </section>

        <!-- Forum Posts Section -->
        <section class="forum-posts" id="forumPosts">
            <div class="posts-header">
                <div class="posts-title">
                    <h2 id="categoryTitle">
                        {% if current_category %}
                            {{ current_category|title }} Discussions
                        {% else %}
                            Recent Discussions
                        {% endif %}
                    </h2>
                    <button class="btn btn-primary" onclick="showNewPostModal()">
                        <i class="fas fa-plus"></i> New Post
                    </button>
                </div>
                <div class="posts-filter">
                    <select id="sortFilter" onchange="sortPosts()">
                        <option value="recent" {% if current_sort == 'recent' %}selected{% endif %}>Most Recent</option>
                        <option value="popular" {% if current_sort == 'popular' %}selected{% endif %}>Most Popular</option>
                        <option value="replies" {% if current_sort == 'replies' %}selected{% endif %}>Most Replies</option>
                    </select>
                </div>
            </div>

            <div class="posts-list" id="postsList">
                {% for post in posts %}
                <div class="post-card">
                    <div class="post-header">
                        <a href="{{ post.get_absolute_url }}" class="post-title">{{ post.title }}</a>
                        <span class="post-category">{{ post.get_category_display }}</span>
                    </div>
                    <div class="post-meta">
                        <span><i class="fas fa-user"></i> {{ post.get_author_display }}</span>
                        <span><i class="fas fa-calendar"></i> {{ post.created_at|date:"M d, Y" }}</span>
                        <span><i class="fas fa-eye"></i> {{ post.views }} views</span>
                        <span><i class="fas fa-comments"></i> {{ post.reply_count }} replies</span>
                        {% if post.is_pinned %}
                        <span><i class="fas fa-thumbtack"></i> Pinned</span>
                        {% endif %}
                    </div>
                    <div class="post-excerpt">
                        {{ post.content|truncatewords:30 }}
                    </div>
                    <div class="post-actions">
                        <button class="post-action" onclick="showReplyModal({{ post.pk }})">
                            <i class="fas fa-reply"></i> Reply
                        </button>
                        <button class="post-action" onclick="likePost({{ post.pk }})">
                            <i class="fas fa-heart"></i> {{ post.likes }}
                        </button>
                    </div>
                </div>
                {% empty %}
                <div class="post-card">
                    <p>No posts yet in this category. Be the first to start a discussion!</p>
                </div>
                {% endfor %}
            </div>
        </section>
    </div>
</main>

<!-- New Post Modal -->
<div class="modal" id="newPostModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Create New Post</h3>
            <button class="modal-close" onclick="closeNewPostModal()">&times;</button>
        </div>
        <form class="modal-body" id="newPostForm">
            {% csrf_token %}
            <div class="form-group">
                <label for="postCategory">Category:</label>
                <select id="postCategory" class="form-input" required>
                    <option value="">Select a category</option>
                    {% for category_code, category_name in categories %}
                    <option value="{{ category_code }}">{{ category_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="postTitle">Title:</label>
                <input type="text" id="postTitle" class="form-input" placeholder="Enter post title" required>
            </div>
            <div class="form-group">
                <label for="postContent">Content:</label>
                <textarea id="postContent" class="form-input" rows="6" placeholder="Share your thoughts, questions, or experiences..." required></textarea>
            </div>
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="anonymousPost">
                    <span class="checkmark"></span>
                    Post anonymously
                </label>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeNewPostModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Post</button>
            </div>
        </form>
    </div>
</div>

<!-- Reply Modal -->
<div class="modal" id="replyModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Reply to Post</h3>
            <button class="modal-close" onclick="closeReplyModal()">&times;</button>
        </div>
        <form class="modal-body" id="replyForm">
            {% csrf_token %}
            <div class="original-post" id="originalPost">
                <!-- Original post content will be shown here -->
            </div>
            <div class="form-group">
                <label for="replyContent">Your Reply:</label>
                <textarea id="replyContent" class="form-input" rows="4" placeholder="Write your reply..." required></textarea>
            </div>
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="anonymousReply">
                    <span class="checkmark"></span>
                    Reply anonymously
                </label>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeReplyModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Reply</button>
            </div>
        </form>
    </div>
</div>

<!-- Footer -->
<footer class="footer">
    <div class="container">
        <div class="footer-content">
            <div class="footer-section">
                <div class="footer-logo">
                    <i class="fas fa-baby"></i>
                    <span>MATERNIFY</span>
                </div>
                <p>Empowering mothers with knowledge and support throughout their journey.</p>
            </div>
            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul>
                    <li><a href="{% url 'core:dashboard' %}">Dashboard</a></li>
                    <li><a href="#">Pregnancy Care</a></li>
                    <li><a href="#">Infant Care</a></li>
                    <li><a href="{% url 'core:tools' %}">Tools</a></li>
                    <li><a href="{% url 'core:forum' %}">Community Forum</a></li>
                    <li><a href="#">Resources</a></li>
                </ul>
            </div>
            <div class="footer-section">
                <h3>Contact</h3>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-phone"></i> +****************</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2024 MATERNIFY. All rights reserved.</p>
        </div>
    </div>
</footer>

{% block extra_js %}
<script src="{% static 'js/main.js' %}"></script>
<script src="{% static 'js/auth.js' %}"></script>
<script src="{% static 'js/forum.js' %}"></script>
{% endblock %}
{% endblock %}
