/* Dashboard Styles */
.dashboard-main {
    padding-top: 100px;
    min-height: 100vh;
    background: #f8f9fa;
}

.welcome-section {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 3rem;
    border-radius: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.welcome-content h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.welcome-content p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.welcome-stats {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 150px;
    backdrop-filter: blur(10px);
}

.stat-card i {
    font-size: 2rem;
    color: #ffb3d9;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 3rem;
}

.quick-actions h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
}

.action-card i {
    font-size: 3rem;
    color: #ffb3d9;
    margin-bottom: 1rem;
}

.action-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.action-card p {
    color: #666;
    font-size: 0.95rem;
}

/* Tips Section */
.tips-section {
    margin-bottom: 3rem;
}

.tips-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.tips-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.tip-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.tip-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ffb3d9, #ff99cc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.tip-icon i {
    font-size: 1.5rem;
    color: white;
}

.tip-content h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.tip-content p {
    color: #666;
    line-height: 1.6;
}

/* Activity Section */
.activity-section {
    margin-bottom: 3rem;
}

.activity-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.activity-list {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.activity-item {
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 50px;
    height: 50px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-icon i {
    font-size: 1.2rem;
    color: #a8d8ea;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    color: #333;
}

.activity-content p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #999;
}

/* Reminders Section */
.reminders-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.reminders-list {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.reminder-item {
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

.reminder-item:last-child {
    border-bottom: none;
}

.reminder-date {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ffb3d9, #ff99cc);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.reminder-date .day {
    font-size: 1.2rem;
    font-weight: bold;
    line-height: 1;
}

.reminder-date .month {
    font-size: 0.8rem;
    text-transform: uppercase;
}

.reminder-content {
    flex: 1;
}

.reminder-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    color: #333;
}

.reminder-content p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.reminder-time {
    font-size: 0.8rem;
    color: #999;
}

.reminder-action {
    flex-shrink: 0;
}

.btn-small {
    padding: 0.5rem 1rem;
    background: #a8d8ea;
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-small:hover {
    background: #95d0e0;
}

/* User greeting in navigation */
.user-greeting {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 20px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
}

.logout-btn {
    background: rgba(255, 179, 217, 0.2) !important;
    border: 1px solid rgba(255, 179, 217, 0.3) !important;
    border-radius: 20px !important;
}

.logout-btn:hover {
    background: #ffb3d9 !important;
    color: white !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-section {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1rem;
    }

    .welcome-content h1 {
        font-size: 2rem;
    }

    .welcome-stats {
        justify-content: center;
    }

    .stat-card {
        min-width: 120px;
        padding: 1rem;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }

    .tips-container {
        grid-template-columns: 1fr;
    }

    .tip-card {
        flex-direction: column;
        text-align: center;
    }

    .activity-item,
    .reminder-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .reminder-item {
        flex-direction: row;
        text-align: left;
    }
}

/* Tools Page Styles */
.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.tools-section {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.tool-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.tool-header {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.tool-header i {
    font-size: 2rem;
    color: #ffb3d9;
}

.tool-header h2 {
    font-size: 1.8rem;
    margin: 0;
}

.tool-content {
    padding: 2rem;
}

.tool-content > p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.tool-form {
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-input,
.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: #f8f9fa;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #a8d8ea;
    background: white;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.input-group .form-input {
    flex: 1;
}

.input-unit {
    background: #f8f9fa;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    color: #666;
    font-weight: 500;
}

.tool-result {
    margin-top: 2rem;
}

.result-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    border-left: 5px solid #ffb3d9;
}

.result-card h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
}

.result-highlight {
    text-align: center;
    margin-bottom: 2rem;
}

.result-date,
.result-bmi,
.result-range {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    color: #a8d8ea;
    margin-bottom: 0.5rem;
}

.result-category {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    margin: 0 auto;
    width: fit-content;
}

.result-category.underweight {
    background: #e3f2fd;
    color: #1976d2;
}

.result-category.normal {
    background: #e8f5e8;
    color: #388e3c;
}

.result-category.overweight {
    background: #fff3e0;
    color: #f57c00;
}

.result-category.obese {
    background: #ffebee;
    color: #d32f2f;
}

.result-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.detail-label {
    font-weight: 600;
    color: #333;
}

.detail-value {
    font-weight: bold;
    color: #a8d8ea;
}

.bmi-chart {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.bmi-range {
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.bmi-range.underweight {
    background: #e3f2fd;
    color: #1976d2;
}

.bmi-range.normal {
    background: #e8f5e8;
    color: #388e3c;
}

.bmi-range.overweight {
    background: #fff3e0;
    color: #f57c00;
}

.bmi-range.obese {
    background: #ffebee;
    color: #d32f2f;
}

.bmi-range.active {
    transform: scale(1.05);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.range-label {
    display: block;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.range-value {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
}

.result-note {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1rem;
    color: #856404;
}

.result-note p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .tool-header {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
    }

    .tool-content {
        padding: 1.5rem;
    }

    .result-details {
        grid-template-columns: 1fr;
    }

    .bmi-chart {
        grid-template-columns: repeat(2, 1fr);
    }

    .result-date,
    .result-bmi,
    .result-range {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .dashboard-main {
        padding-top: 80px;
    }

    .welcome-section {
        margin: 1rem;
        border-radius: 15px;
    }

    .container {
        padding: 0 1rem;
    }

    .bmi-chart {
        grid-template-columns: 1fr;
    }

    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Pregnancy Care Page Styles */
.trimester-nav {
    margin-bottom: 3rem;
}

.nav-tabs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.tab-btn {
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 15px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.tab-btn:hover {
    border-color: #a8d8ea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.tab-btn.active {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    border-color: transparent;
}

.tab-btn i {
    font-size: 2rem;
    color: #ffb3d9;
}

.tab-btn.active i {
    color: white;
}

.tab-btn .week-range {
    font-size: 0.9rem;
    opacity: 0.8;
}

.trimester-content {
    display: none;
}

.trimester-content.active {
    display: block;
}

.trimester-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
}

.trimester-header h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.trimester-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
}

.care-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.care-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.care-card:hover {
    transform: translateY(-5px);
}

.care-header {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.care-header i {
    font-size: 1.5rem;
    color: #ffb3d9;
}

.care-header h3 {
    font-size: 1.3rem;
    margin: 0;
}

.care-content {
    padding: 2rem;
}

.care-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.care-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
    line-height: 1.6;
    color: #555;
}

.care-list li:last-child {
    border-bottom: none;
}

.care-list li strong {
    color: #333;
    font-weight: 600;
}

/* Warning Section */
.warning-section {
    margin-top: 3rem;
}

.warning-card {
    background: #fff3cd;
    border: 2px solid #ffeaa7;
    border-radius: 15px;
    overflow: hidden;
}

.warning-header {
    background: #f39c12;
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.warning-header i {
    font-size: 1.5rem;
}

.warning-header h3 {
    font-size: 1.3rem;
    margin: 0;
}

.warning-content {
    padding: 2rem;
}

.warning-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.warning-item {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #f39c12;
}

.warning-item strong {
    display: block;
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.1rem;
}

.warning-item ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.warning-item li {
    padding: 0.25rem 0;
    color: #666;
    position: relative;
    padding-left: 1.5rem;
}

.warning-item li::before {
    content: '•';
    color: #f39c12;
    font-weight: bold;
    position: absolute;
    left: 0;
}

@media (max-width: 768px) {
    .nav-tabs {
        grid-template-columns: 1fr;
    }

    .care-categories {
        grid-template-columns: 1fr;
    }

    .warning-grid {
        grid-template-columns: 1fr;
    }

    .trimester-header h2 {
        font-size: 2rem;
    }

    .tab-btn {
        padding: 1rem;
    }
}

/* Infant Care Page Styles */
.infant-nav {
    margin-bottom: 3rem;
}

.infant-section {
    display: none;
}

.infant-section.active {
    display: block;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
}

/* Essentials Checklist */
.essentials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.essential-category {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.category-header {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.category-header i {
    font-size: 1.5rem;
    color: #ffb3d9;
}

.category-header h3 {
    font-size: 1.3rem;
    margin: 0;
}

.checklist {
    padding: 2rem;
}

.checklist-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.checklist-item:hover {
    background-color: #f8f9fa;
}

.checklist-item:last-child {
    border-bottom: none;
}

.checklist-item input[type="checkbox"] {
    margin-right: 1rem;
    transform: scale(1.2);
    accent-color: #a8d8ea;
}

.checkmark {
    flex: 1;
    color: #555;
    line-height: 1.5;
}

.checklist-item input[type="checkbox"]:checked + .checkmark {
    color: #333;
    text-decoration: line-through;
    opacity: 0.7;
}

/* Feeding Guide */
.feeding-guide {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.feeding-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.feeding-tab {
    flex: 1;
    padding: 1rem;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 500;
}

.feeding-tab:hover {
    background: #e9ecef;
}

.feeding-tab.active {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
}

.feeding-tab i {
    font-size: 1.2rem;
}

.feeding-content {
    display: none;
    padding: 2rem;
}

.feeding-content.active {
    display: block;
}

.feeding-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feeding-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    border-left: 4px solid #a8d8ea;
}

.feeding-card h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.feeding-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feeding-card li {
    padding: 0.5rem 0;
    color: #555;
    line-height: 1.5;
    position: relative;
    padding-left: 1.5rem;
}

.feeding-card li::before {
    content: '•';
    color: #a8d8ea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Schedule Timeline */
.schedule-timeline {
    max-width: 800px;
    margin: 0 auto;
}

.timeline-item {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    align-items: flex-start;
}

.timeline-marker {
    background: linear-gradient(135deg, #ffb3d9, #ff99cc);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    white-space: nowrap;
    min-width: 100px;
    text-align: center;
}

.timeline-content {
    flex: 1;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #ffb3d9;
}

.timeline-content h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.timeline-content p {
    margin: 0.5rem 0;
    color: #555;
    line-height: 1.5;
}

/* Vaccination Timeline */
.vaccination-timeline {
    max-width: 900px;
    margin: 0 auto 3rem;
}

.vaccine-milestone {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    align-items: flex-start;
}

.milestone-age {
    background: linear-gradient(135deg, #a8d8ea, #95d0e0);
    color: white;
    padding: 1rem;
    border-radius: 15px;
    font-weight: bold;
    min-width: 120px;
    text-align: center;
    font-size: 1.1rem;
}

.milestone-content {
    flex: 1;
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
    border-left: 4px solid #a8d8ea;
}

.milestone-content h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.vaccine-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.vaccine-list li {
    padding: 0.5rem 0;
    color: #555;
    line-height: 1.5;
    position: relative;
    padding-left: 1.5rem;
}

.vaccine-list li::before {
    content: '💉';
    position: absolute;
    left: 0;
}

.vaccine-note {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.75rem;
    border-radius: 8px;
    margin-top: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.vaccine-reminders {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.reminder-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    border-left: 4px solid #ffb3d9;
}

.reminder-card i {
    font-size: 2rem;
    color: #ffb3d9;
    margin-bottom: 1rem;
}

.reminder-card h4 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.reminder-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.reminder-card li {
    padding: 0.5rem 0;
    color: #555;
    line-height: 1.5;
    position: relative;
    padding-left: 1.5rem;
}

.reminder-card li::before {
    content: '✓';
    color: #ffb3d9;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Growth Tracking */
.growth-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.growth-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.growth-header {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.growth-header i {
    font-size: 1.5rem;
    color: #ffb3d9;
}

.growth-header h3 {
    font-size: 1.3rem;
    margin: 0;
}

.growth-content {
    padding: 2rem;
}

.milestone-timeline {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.milestone-item {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.milestone-age-badge {
    background: linear-gradient(135deg, #ffb3d9, #ff99cc);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    white-space: nowrap;
    min-width: 100px;
    text-align: center;
    font-size: 0.9rem;
}

.milestone-details {
    flex: 1;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #ffb3d9;
}

.milestone-details h5 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.milestone-details ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.milestone-details li {
    padding: 0.25rem 0;
    color: #555;
    line-height: 1.5;
    position: relative;
    padding-left: 1.5rem;
}

.milestone-details li::before {
    content: '•';
    color: #ffb3d9;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Development Tabs */
.development-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 10px;
    margin-bottom: 2rem;
    overflow: hidden;
}

.dev-tab {
    flex: 1;
    padding: 1rem;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #666;
}

.dev-tab:hover {
    background: #e9ecef;
}

.dev-tab.active {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
}

.development-content {
    display: none;
}

.development-content.active {
    display: block;
}

.dev-milestone {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #a8d8ea;
}

.dev-milestone strong {
    color: #333;
    font-size: 1.1rem;
    display: block;
    margin-bottom: 1rem;
}

.dev-milestone ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.dev-milestone li {
    padding: 0.25rem 0;
    color: #555;
    line-height: 1.5;
    position: relative;
    padding-left: 1.5rem;
}

.dev-milestone li::before {
    content: '✓';
    color: #a8d8ea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Growth Tracking Form */
.tracking-form {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.tracking-form h4 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.growth-records {
    margin-top: 2rem;
}

.growth-records h4 {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.records-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.growth-record {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-left: 4px solid #a8d8ea;
    position: relative;
}

.record-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.record-header strong {
    color: #333;
    font-size: 1.1rem;
}

.record-date {
    color: #666;
    font-size: 0.9rem;
}

.record-details {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.record-details span {
    color: #555;
    font-size: 0.95rem;
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.delete-record {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.delete-record:hover {
    background: #c82333;
}

.no-records {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 2rem;
}

/* Responsive Design for Infant Care */
@media (max-width: 768px) {
    .essentials-grid {
        grid-template-columns: 1fr;
    }

    .feeding-tabs {
        flex-direction: column;
    }

    .feeding-cards {
        grid-template-columns: 1fr;
    }

    .timeline-item,
    .vaccine-milestone {
        flex-direction: column;
        gap: 1rem;
    }

    .timeline-marker,
    .milestone-age {
        align-self: flex-start;
        min-width: auto;
    }

    .vaccine-reminders {
        grid-template-columns: 1fr;
    }

    .growth-categories {
        grid-template-columns: 1fr;
    }

    .milestone-item {
        flex-direction: column;
        gap: 1rem;
    }

    .milestone-age-badge {
        align-self: flex-start;
        min-width: auto;
    }

    .development-tabs {
        flex-direction: column;
    }

    .record-details {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Forum Page Styles */
.forum-categories {
    margin-bottom: 3rem;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.category-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #a8d8ea, #aa96da);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.category-icon i {
    font-size: 2rem;
    color: white;
}

.category-card h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1rem;
}

.category-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.category-stats {
    display: flex;
    justify-content: space-around;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
}

.category-stats span {
    color: #999;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.category-stats i {
    color: #a8d8ea;
}

/* Forum Posts */
.forum-posts {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.posts-header {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.posts-title {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.posts-title h2 {
    font-size: 1.8rem;
    margin: 0;
}

.posts-filter select {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 20px;
    background: rgba(255,255,255,0.2);
    color: white;
    cursor: pointer;
}

.posts-filter select option {
    background: #333;
    color: white;
}

.posts-list {
    padding: 2rem;
}

.post-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid #a8d8ea;
}

.post-card:hover {
    background: #f0f0f0;
    transform: translateX(5px);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.post-author {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.post-author i {
    color: #a8d8ea;
    font-size: 1.2rem;
}

.post-time {
    color: #999;
    font-size: 0.8rem;
}

.category-tag {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    color: white;
}

.category-pregnancy { background: #ff6b9d; }
.category-newborn { background: #4ecdc4; }
.category-feeding { background: #45b7d1; }
.category-health { background: #96ceb4; }
.category-support { background: #feca57; }
.category-general { background: #a8d8ea; }

.post-content {
    margin-bottom: 1rem;
}

.post-title {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.post-excerpt {
    color: #666;
    line-height: 1.6;
}

.post-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.post-stats {
    display: flex;
    gap: 1rem;
    color: #999;
    font-size: 0.9rem;
}

.post-stats span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.post-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: none;
    border: 1px solid #ddd;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    color: #666;
}

.action-btn:hover {
    background: #a8d8ea;
    color: white;
    border-color: #a8d8ea;
}

.no-posts {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-posts i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-posts h3 {
    margin-bottom: 1rem;
    color: #999;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.original-post {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border-left: 4px solid #a8d8ea;
}

.original-post-content h4 {
    color: #333;
    margin-bottom: 1rem;
}

.original-post-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    color: #666;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
}

/* Responsive Design for Forum */
@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
    }

    .posts-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .posts-title {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .post-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .post-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .post-actions {
        justify-content: center;
    }

    .modal {
        padding: 1rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .modal-footer {
        flex-direction: column;
    }
}

/* Resources Page Styles */
.resource-categories {
    margin-bottom: 3rem;
}

.category-tabs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.resource-tab {
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 15px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #666;
}

.resource-tab:hover {
    border-color: #a8d8ea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.resource-tab.active {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    border-color: transparent;
}

.resource-tab i {
    font-size: 1.5rem;
    color: #ffb3d9;
}

.resource-tab.active i {
    color: white;
}

.resource-section {
    display: none;
}

.resource-section.active {
    display: block;
}

/* Video Section */
.video-categories {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.video-filter {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    padding: 1.5rem;
}

.video-filter select {
    background: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    font-size: 1rem;
    cursor: pointer;
    min-width: 200px;
}

.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    padding: 2rem;
}

.video-card {
    background: #f8f9fa;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.video-card:hover {
    transform: translateY(-5px);
}

.video-thumbnail {
    position: relative;
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.video-thumbnail iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.video-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    font-size: 0.8rem;
}

.video-info {
    padding: 1.5rem;
}

.video-info h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.video-info p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.video-category {
    background: #a8d8ea;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.video-views {
    color: #999;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Articles Section */
.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.article-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
}

.article-image {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.article-content {
    padding: 2rem;
}

.article-category {
    background: #ffb3d9;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    display: inline-block;
    margin-bottom: 1rem;
}

.article-content h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1rem;
}

.article-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.article-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: #999;
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.read-more-btn {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: transform 0.3s ease;
}

.read-more-btn:hover {
    transform: translateY(-2px);
}

/* Downloads Section */
.downloads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.download-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    display: flex;
    gap: 1.5rem;
    align-items: center;
    transition: transform 0.3s ease;
}

.download-card:hover {
    transform: translateY(-3px);
}

.download-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ffb3d9, #ff99cc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.download-icon i {
    font-size: 1.5rem;
    color: white;
}

.download-info {
    flex: 1;
}

.download-info h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.download-info p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 0.75rem;
}

.download-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    font-size: 0.9rem;
    color: #999;
}

.download-btn {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 500;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.download-btn:hover {
    transform: translateY(-2px);
}

/* Webinars Section */
.webinars-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.webinar-tabs {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.webinar-tab {
    flex: 1;
    padding: 1.5rem;
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-weight: 500;
    color: #666;
}

.webinar-tab:hover {
    background: #e9ecef;
}

.webinar-tab.active {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
}

.webinar-content {
    display: none;
    padding: 2rem;
}

.webinar-content.active {
    display: block;
}

.webinars-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.webinar-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 1.5rem;
    display: flex;
    gap: 1.5rem;
    align-items: center;
    transition: transform 0.3s ease;
}

.webinar-card:hover {
    transform: translateX(5px);
}

.webinar-date {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ffb3d9, #ff99cc);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.date-day {
    font-size: 1.2rem;
    font-weight: bold;
    line-height: 1;
}

.date-month {
    font-size: 0.8rem;
    text-transform: uppercase;
}

.webinar-thumbnail {
    width: 80px;
    height: 60px;
    background: linear-gradient(135deg, #a8d8ea, #aa96da);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    flex-shrink: 0;
}

.webinar-thumbnail i {
    font-size: 2rem;
    color: white;
}

.duration {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 0.125rem 0.25rem;
    border-radius: 3px;
    font-size: 0.7rem;
}

.webinar-info {
    flex: 1;
}

.webinar-info h3 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.webinar-info p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 0.75rem;
}

.webinar-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #999;
    flex-wrap: wrap;
}

.webinar-details span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.webinar-action {
    flex-shrink: 0;
}

.register-btn,
.watch-btn {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: transform 0.3s ease;
}

.register-btn:hover,
.watch-btn:hover {
    transform: translateY(-2px);
}

.register-btn:disabled {
    background: #28a745;
    cursor: not-allowed;
    transform: none;
}

/* Article Modal */
.article-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    padding: 2rem;
}

.article-modal-content {
    background: white;
    border-radius: 15px;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.article-modal-header {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.article-modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.article-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.article-modal-close:hover {
    background: rgba(255,255,255,0.2);
}

.article-modal-body {
    padding: 2rem;
    line-height: 1.8;
    color: #666;
}

/* Download Notification */
.download-notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    z-index: 2000;
    animation: slideIn 0.3s ease;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design for Resources */
@media (max-width: 768px) {
    .category-tabs {
        grid-template-columns: repeat(2, 1fr);
    }

    .videos-grid {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .downloads-grid {
        grid-template-columns: 1fr;
    }

    .download-card {
        flex-direction: column;
        text-align: center;
    }

    .webinar-tabs {
        flex-direction: column;
    }

    .webinar-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .webinar-details {
        justify-content: center;
    }

    .article-modal {
        padding: 1rem;
    }

    .article-modal-body {
        padding: 1.5rem;
    }
}

/* Infant Care Guidance Styles */
.guidance-timeline {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.guidance-month {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.guidance-month:hover {
    transform: translateY(-3px);
}

.month-header {
    background: linear-gradient(135deg, #a8d8ea 0%, #aa96da 100%);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.month-number {
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    flex-shrink: 0;
}

.month-title h3 {
    margin: 0 0 0.25rem 0;
    font-size: 1.5rem;
}

.month-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
}

.month-content {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.guidance-category {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
}

.guidance-category h4 {
    color: #333;
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.guidance-category h4 i {
    color: #ffb3d9;
    font-size: 1rem;
}

.guidance-category ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.guidance-category li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    color: #666;
    line-height: 1.5;
    position: relative;
    padding-left: 1.5rem;
}

.guidance-category li:last-child {
    border-bottom: none;
}

.guidance-category li:before {
    content: "•";
    color: #a8d8ea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Responsive Design for Guidance */
@media (max-width: 768px) {
    .month-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .month-content {
        grid-template-columns: 1fr;
        padding: 1.5rem;
    }

    .guidance-category {
        padding: 1rem;
    }
}

/* Alert Notifications */
.alert {
    position: fixed;
    top: 100px;
    right: 20px;
    max-width: 400px;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
    z-index: 2000;
    animation: slideInAlert 0.3s ease;
}

.alert-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.alert-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: auto;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.alert-close:hover {
    background: rgba(0,0,0,0.1);
}

@keyframes slideInAlert {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Tools Page Enhancements */
.tool-result {
    margin-top: 2rem;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-highlight {
    text-align: center;
    margin: 1.5rem 0;
}

.result-date,
.result-bmi {
    font-size: 2rem;
    font-weight: bold;
    color: #a8d8ea;
    display: block;
}

.result-category {
    font-size: 1.2rem;
    font-weight: 500;
    margin-top: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    display: inline-block;
}

.result-category.underweight {
    background: #e3f2fd;
    color: #1976d2;
}

.result-category.normal {
    background: #e8f5e8;
    color: #388e3c;
}

.result-category.overweight {
    background: #fff3e0;
    color: #f57c00;
}

.result-category.obese {
    background: #ffebee;
    color: #d32f2f;
}

.bmi-range {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    margin: 0.5rem 0;
    border-radius: 8px;
    background: #f8f9fa;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.bmi-range.active {
    border-color: #a8d8ea;
    background: #f0f8ff;
    transform: scale(1.02);
}

.range-label {
    font-weight: 500;
}

.range-value {
    color: #666;
    font-size: 0.9rem;
}

.result-range {
    font-size: 1.8rem;
    font-weight: bold;
    color: #aa96da;
    display: block;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #666;
}

.detail-value {
    font-weight: 600;
    color: #333;
}

.result-note {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1.5rem;
    border-left: 4px solid #a8d8ea;
}

.result-note p {
    margin: 0;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.5;
}

/* Form Enhancements */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.input-group {
    display: flex;
    align-items: stretch;
    gap: 0;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
}

.input-group .form-input {
    border: none;
    border-radius: 0;
    flex: 1;
    margin: 0;
}

.input-group .form-input:focus {
    border: none;
    box-shadow: none;
}

.input-group:focus-within {
    border-color: #a8d8ea;
    box-shadow: 0 0 0 3px rgba(168, 216, 234, 0.1);
}

.input-unit {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    color: #666;
    font-weight: 500;
    min-width: 50px;
    text-align: center;
    border-left: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-select {
    background: #f8f9fa;
    border: none;
    border-left: 1px solid #e1e5e9;
    border-radius: 0;
    padding: 0.75rem;
    font-size: 1rem;
    cursor: pointer;
    min-width: 80px;
    color: #666;
    font-weight: 500;
}

.form-select:focus {
    outline: none;
    background: #e9ecef;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #a8d8ea;
    box-shadow: 0 0 0 3px rgba(168, 216, 234, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

/* Responsive improvements for tools */
@media (max-width: 768px) {
    .alert {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .input-group {
        flex-direction: row;
        align-items: stretch;
    }

    .result-date,
    .result-bmi,
    .result-range {
        font-size: 1.5rem;
    }

    .tool-card {
        margin-bottom: 2rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }
}

/* Dashboard Content Adjustments */
.dashboard-main {
    margin-top: 80px;
    padding: 2rem;
    min-height: calc(100vh - 80px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-main {
        padding: 1rem;
    }

    .nav-container {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .nav-logo span {
        font-size: 1.2rem;
    }
}

/* Enhanced BMI Calculator Styles */
.result-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.detail-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
}

.detail-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.result-recommendations {
    margin-top: 20px;
    padding: 20px;
    background: #e8f4fd;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.result-recommendations h4 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.result-recommendations p {
    margin: 8px 0;
    color: #34495e;
    line-height: 1.5;
}

/* Checkbox Styling */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 1rem;
    color: #2c3e50;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

/* Nutrition Grid Styles */
.nutrition-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.nutrition-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.nutrition-item:hover {
    border-color: #3498db;
    transform: translateY(-2px);
}

.nutrition-label {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nutrition-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
}

/* Water Progress Styles */
.water-progress {
    display: flex;
    align-items: center;
    gap: 30px;
    margin: 20px 0;
}

.progress-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(#3498db 0deg, #e9ecef 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 90px;
    height: 90px;
    background: white;
    border-radius: 50%;
    position: absolute;
}

.progress-text {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    z-index: 1;
}

.progress-details {
    flex: 1;
}

.progress-details p {
    margin: 8px 0;
    font-size: 1rem;
    color: #2c3e50;
}

.progress-details span {
    font-weight: 600;
    color: #3498db;
}

/* Blood Pressure Category Styles */
.result-category.normal {
    color: #27ae60;
}

.result-category.elevated {
    color: #f39c12;
}

.result-category.high {
    color: #e74c3c;
}

.result-category.crisis {
    color: #8e44ad;
    font-weight: 700;
}

/* Enhanced Tool Cards */
.tool-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.tool-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.tool-header i {
    font-size: 1.5rem;
}

.tool-header h2 {
    margin: 0;
    font-size: 1.3rem;
}

.tool-content {
    padding: 25px;
}

/* Form Enhancements */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.input-group {
    display: flex;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    transition: border-color 0.3s ease;
}

.input-group:focus-within {
    border-color: #3498db;
}

.form-input, .form-select {
    flex: 1;
    padding: 12px 15px;
    border: none;
    font-size: 1rem;
    background: white;
}

.form-input:focus, .form-select:focus {
    outline: none;
}

.form-select {
    background: #f8f9fa;
    border-left: 1px solid #e9ecef;
    min-width: 80px;
}

/* Button Enhancements */
.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* Result Section Enhancements */
.tool-result {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.result-highlight {
    text-align: center;
    margin-bottom: 25px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.result-bmi, .result-bp {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
}

.result-category {
    font-size: 1.2rem;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* BMI Range Indicators */
.bmi-ranges {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.bmi-range {
    padding: 12px;
    text-align: center;
    border-radius: 8px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.bmi-range.underweight {
    background: #e3f2fd;
    color: #1976d2;
}

.bmi-range.normal {
    background: #e8f5e8;
    color: #388e3c;
}

.bmi-range.overweight {
    background: #fff3e0;
    color: #f57c00;
}

.bmi-range.obese {
    background: #ffebee;
    color: #d32f2f;
}

.bmi-range.active {
    border-color: currentColor;
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.range-label {
    display: block;
    font-weight: 600;
    margin-bottom: 4px;
}

.range-value {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Responsive Design for New Tools */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .nutrition-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .water-progress {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .result-details {
        grid-template-columns: 1fr;
    }

    .bmi-ranges {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* About Page Specific Styles */
.statistics {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 4rem 0;
    margin: 3rem 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card i {
    font-size: 2.5rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Team Section */
.team {
    padding: 4rem 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.team-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.team-card:hover {
    transform: translateY(-5px);
}

.team-image {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
}

.team-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3498db, #2980b9);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.team-placeholder i {
    font-size: 3rem;
    color: white;
}

.team-info h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.team-role {
    color: #3498db;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.team-specialization {
    color: #666;
    font-style: italic;
    margin-bottom: 1rem;
}

.team-bio {
    color: #555;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.team-experience {
    color: #27ae60;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Testimonials Section */
.testimonials {
    background: #f8f9fa;
    padding: 4rem 0;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.testimonial-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    position: relative;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 20px;
    font-size: 4rem;
    color: #3498db;
    opacity: 0.3;
}

.testimonial-content p {
    font-style: italic;
    color: #555;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.testimonial-author {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.author-info h4 {
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.author-info p {
    color: #666;
    font-size: 0.9rem;
    margin: 0;
}

.testimonial-rating {
    color: #f39c12;
}

/* Contact Section */
.contact {
    padding: 4rem 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    margin-top: 2rem;
}

.contact-info h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.contact-item i {
    color: #3498db;
    font-size: 1.2rem;
    width: 20px;
}

.contact-form-container {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.contact-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-form .form-group {
    margin-bottom: 1rem;
}

.contact-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

.contact-form .form-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.contact-form .form-input:focus {
    outline: none;
    border-color: #3498db;
}

.contact-form textarea {
    resize: vertical;
    min-height: 120px;
}

/* Alert Styles */
.alert {
    position: fixed;
    top: 100px;
    right: 20px;
    max-width: 400px;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    z-index: 2000;
    animation: slideInAlert 0.3s ease;
}

.alert-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: auto;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.alert-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

@keyframes slideInAlert {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design for About Page */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-form .form-row {
        grid-template-columns: 1fr;
    }

    .alert {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}
