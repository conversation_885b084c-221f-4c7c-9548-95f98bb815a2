from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class Article(models.Model):
    """Articles for pregnancy and infant care"""
    CATEGORY_CHOICES = [
        ('pregnancy', 'Pregnancy Care'),
        ('infant', 'Infant Care'),
        ('nutrition', 'Nutrition'),
        ('exercise', 'Exercise'),
        ('mental_health', 'Mental Health'),
        ('general', 'General Health'),
    ]
    
    title = models.CharField(max_length=255)
    slug = models.SlugField(unique=True)
    content = models.TextField()
    excerpt = models.TextField(max_length=500, blank=True)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    author = models.ForeignKey(User, on_delete=models.CASCADE)
    featured_image = models.ImageField(upload_to='articles/', null=True, blank=True)
    is_published = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title


class ForumPost(models.Model):
    """Community forum posts"""
    CATEGORY_CHOICES = [
        ('pregnancy', 'Pregnancy Journey'),
        ('newborn', 'Newborn Care'),
        ('feeding', 'Feeding & Nutrition'),
        ('health', 'Health & Wellness'),
        ('support', 'Emotional Support'),
        ('general', 'General Discussion'),
    ]

    title = models.CharField(max_length=255)
    content = models.TextField()
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='forum_posts')
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='general')
    is_pinned = models.BooleanField(default=False)
    is_anonymous = models.BooleanField(default=False)
    views = models.PositiveIntegerField(default=0)
    likes = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-is_pinned', '-created_at']

    def __str__(self):
        return self.title

    @property
    def reply_count(self):
        return self.replies.count()

    def get_author_display(self):
        if self.is_anonymous:
            return "Anonymous"
        return self.author.get_full_name() or self.author.username

    def increment_views(self):
        self.views += 1
        self.save(update_fields=['views'])

    def get_absolute_url(self):
        return f'/forum/post/{self.pk}/'


class ForumReply(models.Model):
    """Replies to forum posts"""
    post = models.ForeignKey(ForumPost, on_delete=models.CASCADE, related_name='replies')
    content = models.TextField()
    author = models.ForeignKey(User, on_delete=models.CASCADE, related_name='forum_replies')
    is_anonymous = models.BooleanField(default=False)
    likes = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['created_at']
        verbose_name_plural = 'Forum replies'

    def __str__(self):
        return f"Reply to {self.post.title} by {self.get_author_display()}"

    def get_author_display(self):
        if self.is_anonymous:
            return "Anonymous"
        return self.author.get_full_name() or self.author.username


class Resource(models.Model):
    """Educational resources"""
    RESOURCE_TYPES = [
        ('video', 'Video'),
        ('pdf', 'PDF Document'),
        ('link', 'External Link'),
        ('checklist', 'Checklist'),
        ('guide', 'Guide'),
    ]
    
    title = models.CharField(max_length=255)
    description = models.TextField()
    resource_type = models.CharField(max_length=20, choices=RESOURCE_TYPES)
    file = models.FileField(upload_to='resources/', null=True, blank=True)
    external_url = models.URLField(null=True, blank=True)
    category = models.CharField(max_length=20, choices=Article.CATEGORY_CHOICES)
    is_featured = models.BooleanField(default=False)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        ordering = ['-is_featured', '-created_at']
    
    def __str__(self):
        return self.title


class Appointment(models.Model):
    """Medical appointments"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='appointments')
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    appointment_date = models.DateTimeField()
    doctor_name = models.CharField(max_length=255, blank=True)
    location = models.CharField(max_length=255, blank=True)
    is_completed = models.BooleanField(default=False)
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        ordering = ['appointment_date']
    
    def __str__(self):
        return f"{self.title} - {self.appointment_date.strftime('%Y-%m-%d %H:%M')}"


class TeamMember(models.Model):
    """Team members for About page"""
    name = models.CharField(max_length=100)
    role = models.CharField(max_length=100)
    specialization = models.CharField(max_length=200)
    bio = models.TextField()
    image = models.ImageField(upload_to='team/', blank=True)
    email = models.EmailField(blank=True)
    linkedin_url = models.URLField(blank=True)
    years_experience = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'name']

    def __str__(self):
        return f"{self.name} - {self.role}"


class Testimonial(models.Model):
    """User testimonials for About page"""
    user = models.ForeignKey('accounts.CustomUser', on_delete=models.CASCADE, related_name='testimonials')
    content = models.TextField()
    rating = models.PositiveIntegerField(choices=[(i, i) for i in range(1, 6)], default=5)
    is_featured = models.BooleanField(default=False)
    is_approved = models.BooleanField(default=False)
    user_location = models.CharField(max_length=100, blank=True)
    user_title = models.CharField(max_length=100, blank=True)  # e.g., "Mother of 2"
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Testimonial by {self.user.get_full_name() or self.user.username}"


class CompanyMilestone(models.Model):
    """Company milestones for About page"""
    year = models.CharField(max_length=4)
    title = models.CharField(max_length=200)
    description = models.TextField()
    icon = models.CharField(max_length=50, blank=True)  # FontAwesome icon class
    order = models.PositiveIntegerField(default=0)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['order', 'year']

    def __str__(self):
        return f"{self.year} - {self.title}"


class ContactInquiry(models.Model):
    """Contact form submissions"""
    INQUIRY_TYPES = [
        ('general', 'General Inquiry'),
        ('support', 'Technical Support'),
        ('partnership', 'Partnership'),
        ('feedback', 'Feedback'),
        ('media', 'Media Inquiry'),
    ]

    name = models.CharField(max_length=100)
    email = models.EmailField()
    subject = models.CharField(max_length=200)
    inquiry_type = models.CharField(max_length=20, choices=INQUIRY_TYPES, default='general')
    message = models.TextField()
    is_resolved = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} - {self.subject}"
