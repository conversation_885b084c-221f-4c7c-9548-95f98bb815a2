<?php
/**
 * Authentication API for MATERNIFY PHP Backend
 */

header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

include_once '../config/database.php';
include_once '../models/User.php';

$database = new Database();
$db = $database->getConnection();

if ($db === null) {
    http_response_code(500);
    echo json_encode(["message" => "Database connection failed"]);
    exit();
}

$user = new User($db);

$method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];

// Parse the endpoint
$path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));
$endpoint = end($path_parts);

switch ($method) {
    case 'POST':
        $data = json_decode(file_get_contents("php://input"), true);
        
        if ($endpoint === 'register') {
            handleRegister($user, $data);
        } elseif ($endpoint === 'login') {
            handleLogin($user, $data);
        } elseif ($endpoint === 'logout') {
            handleLogout($data);
        } else {
            http_response_code(404);
            echo json_encode(["message" => "Endpoint not found"]);
        }
        break;
        
    case 'GET':
        if ($endpoint === 'verify') {
            handleVerifyToken($user);
        } else {
            http_response_code(404);
            echo json_encode(["message" => "Endpoint not found"]);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(["message" => "Method not allowed"]);
        break;
}

function handleRegister($user, $data) {
    if (empty($data['username']) || empty($data['email']) || empty($data['password'])) {
        http_response_code(400);
        echo json_encode(["success" => false, "message" => "Username, email, and password are required"]);
        return;
    }

    // Check if user already exists
    if ($user->findByCredentials($data['username']) || $user->findByCredentials($data['email'])) {
        http_response_code(400);
        echo json_encode(["success" => false, "message" => "Username or email already exists"]);
        return;
    }

    $user->username = $data['username'];
    $user->email = $data['email'];
    $user->password_hash = $user->hashPassword($data['password']);
    $user->full_name = $data['full_name'] ?? '';
    $user->due_date = !empty($data['due_date']) ? $data['due_date'] : null;
    $user->is_pregnant = $data['is_pregnant'] ?? false;

    if ($user->create()) {
        $token = generateToken($user->id);
        
        http_response_code(201);
        echo json_encode([
            "success" => true,
            "message" => "User registered successfully",
            "user" => $user->toArray(),
            "token" => $token
        ]);
    } else {
        http_response_code(500);
        echo json_encode(["success" => false, "message" => "Unable to register user"]);
    }
}

function handleLogin($user, $data) {
    if (empty($data['username']) || empty($data['password'])) {
        http_response_code(400);
        echo json_encode(["success" => false, "message" => "Username and password are required"]);
        return;
    }

    if ($user->findByCredentials($data['username'])) {
        if ($user->verifyPassword($data['password'])) {
            $token = generateToken($user->id);
            
            http_response_code(200);
            echo json_encode([
                "success" => true,
                "message" => "Login successful",
                "user" => $user->toArray(),
                "token" => $token
            ]);
        } else {
            http_response_code(401);
            echo json_encode(["success" => false, "message" => "Invalid password"]);
        }
    } else {
        http_response_code(401);
        echo json_encode(["success" => false, "message" => "User not found"]);
    }
}

function handleLogout($data) {
    // In a real application, you would invalidate the token in the database
    http_response_code(200);
    echo json_encode([
        "success" => true,
        "message" => "Logout successful"
    ]);
}

function handleVerifyToken($user) {
    $headers = getallheaders();
    $token = null;
    
    if (isset($headers['Authorization'])) {
        $auth_header = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
            $token = $matches[1];
        }
    }
    
    if (!$token) {
        http_response_code(401);
        echo json_encode(["success" => false, "message" => "No token provided"]);
        return;
    }
    
    $user_id = verifyToken($token);
    if ($user_id) {
        $user->id = $user_id;
        if ($user->readOne()) {
            http_response_code(200);
            echo json_encode([
                "success" => true,
                "user" => $user->toArray()
            ]);
        } else {
            http_response_code(404);
            echo json_encode(["success" => false, "message" => "User not found"]);
        }
    } else {
        http_response_code(401);
        echo json_encode(["success" => false, "message" => "Invalid token"]);
    }
}

function generateToken($user_id) {
    $header = json_encode(['typ' => 'JWT', 'alg' => 'HS256']);
    $payload = json_encode([
        'user_id' => $user_id,
        'exp' => time() + (24 * 60 * 60) // 24 hours
    ]);
    
    $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
    $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));
    
    $signature = hash_hmac('sha256', $base64Header . "." . $base64Payload, 'your-secret-key', true);
    $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));
    
    return $base64Header . "." . $base64Payload . "." . $base64Signature;
}

function verifyToken($token) {
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        return false;
    }
    
    $header = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[0]));
    $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[1]));
    $signature = $parts[2];
    
    $expected_signature = str_replace(['+', '/', '='], ['-', '_', ''], 
        base64_encode(hash_hmac('sha256', $parts[0] . "." . $parts[1], 'your-secret-key', true)));
    
    if ($signature !== $expected_signature) {
        return false;
    }
    
    $payload_data = json_decode($payload, true);
    if ($payload_data['exp'] < time()) {
        return false;
    }
    
    return $payload_data['user_id'];
}
?>
