from django.test import TestCase, Client
from django.contrib.auth import get_user_model
from django.urls import reverse
from .models import Article, ForumPost, Resource

User = get_user_model()


class CoreViewsTest(TestCase):
    """Test core views"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_index_view(self):
        response = self.client.get(reverse('core:index'))
        self.assertEqual(response.status_code, 200)
    
    def test_about_view(self):
        response = self.client.get(reverse('core:about'))
        self.assertEqual(response.status_code, 200)
    
    def test_dashboard_requires_login(self):
        response = self.client.get(reverse('core:dashboard'))
        self.assertRedirects(response, '/accounts/login/?next=/dashboard/')
    
    def test_dashboard_authenticated(self):
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('core:dashboard'))
        self.assertEqual(response.status_code, 200)
    
    def test_pregnancy_care_view(self):
        response = self.client.get(reverse('core:pregnancy_care'))
        self.assertEqual(response.status_code, 200)
    
    def test_infant_care_view(self):
        response = self.client.get(reverse('core:infant_care'))
        self.assertEqual(response.status_code, 200)
    
    def test_tools_requires_login(self):
        response = self.client.get(reverse('core:tools'))
        self.assertRedirects(response, '/accounts/login/?next=/tools/')
    
    def test_forum_view(self):
        response = self.client.get(reverse('core:forum'))
        self.assertEqual(response.status_code, 200)
    
    def test_resources_view(self):
        response = self.client.get(reverse('core:resources'))
        self.assertEqual(response.status_code, 200)


class CoreModelsTest(TestCase):
    """Test core models"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_article_creation(self):
        article = Article.objects.create(
            title='Test Article',
            slug='test-article',
            content='Test content',
            category='pregnancy',
            author=self.user
        )
        self.assertEqual(str(article), 'Test Article')
    
    def test_forum_post_creation(self):
        post = ForumPost.objects.create(
            title='Test Post',
            content='Test content',
            author=self.user
        )
        self.assertEqual(str(post), 'Test Post')
        self.assertEqual(post.reply_count, 0)
    
    def test_resource_creation(self):
        resource = Resource.objects.create(
            title='Test Resource',
            description='Test description',
            resource_type='guide',
            category='pregnancy'
        )
        self.assertEqual(str(resource), 'Test Resource')
