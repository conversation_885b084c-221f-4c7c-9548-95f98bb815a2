// Health Tools JavaScript functionality for Django

document.addEventListener('DOMContentLoaded', function() {
    console.log('Tools page loaded, initializing...');
    initializeTools();
});

function initializeTools() {
    console.log('Initializing tools...');

    // Initialize form handlers
    console.log('Initializing calculators...');
    initializeDueDateCalculator();
    initializeBMICalculator();
    initializeWeightGainCalculator();
    console.log('Tools initialization complete');
}

// Get CSRF token for Django
function getCSRFToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
           document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
           getCookie('csrftoken');
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Due Date Calculator
function initializeDueDateCalculator() {
    console.log('Initializing Due Date Calculator...');
    const form = document.getElementById('dueDateForm');
    if (!form) {
        console.error('Due Date form not found!');
        return;
    }

    console.log('Due Date form found, adding event listener');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('Due Date form submitted');
        calculateDueDate();
    });
}

function calculateDueDate() {
    const lmpDate = document.getElementById('lmpDate').value;

    if (!lmpDate) {
        showAlert('Please enter your last menstrual period date.', 'error');
        return;
    }

    const lmp = new Date(lmpDate);
    const today = new Date();

    // Validate LMP date
    if (lmp > today) {
        showAlert('Last menstrual period date cannot be in the future.', 'error');
        return;
    }

    // Validate reasonable date range (not more than 10 months ago)
    const tenMonthsAgo = new Date();
    tenMonthsAgo.setMonth(tenMonthsAgo.getMonth() - 10);
    if (lmp < tenMonthsAgo) {
        showAlert('Please enter a more recent date. If your pregnancy is longer than 10 months, please consult your healthcare provider.', 'warning');
        return;
    }

    // Send to Django backend
    fetch('/api/calculate-due-date/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken(),
        },
        body: JSON.stringify({
            lmp_date: lmpDate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const result = data.result;

            // Display results
            document.getElementById('calculatedDueDate').textContent = result.due_date_formatted;
            document.getElementById('currentPregnancyWeek').textContent = `Week ${result.weeks_pregnant}`;
            document.getElementById('daysRemaining').textContent = result.days_remaining > 0 ? `${result.days_remaining} days` : 'Due now!';
            document.getElementById('currentTrimester').textContent = result.trimester;

            // Show result
            document.getElementById('dueDateResult').style.display = 'block';

            // Scroll to result smoothly
            setTimeout(() => {
                document.getElementById('dueDateResult').scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 100);

            showAlert('Due date calculated successfully!', 'success');
        } else {
            showAlert(data.error || 'Error calculating due date', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error calculating due date. Please try again.', 'error');
    });
}

// BMI Calculator
function initializeBMICalculator() {
    console.log('Initializing BMI Calculator...');
    const form = document.getElementById('bmiForm');
    if (!form) {
        console.error('BMI form not found!');
        return;
    }

    console.log('BMI form found, adding event listener');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('BMI form submitted');
        calculateBMI();
    });
}

function calculateBMI() {
    console.log('Calculating BMI...');

    const height = parseFloat(document.getElementById('height').value);
    const weight = parseFloat(document.getElementById('weight').value);
    const heightUnit = document.getElementById('heightUnit').value;
    const weightUnit = document.getElementById('weightUnit').value;

    console.log('BMI Input values:', { height, weight, heightUnit, weightUnit });

    if (!height || !weight) {
        console.log('Missing height or weight');
        showAlert('Please enter both height and weight.', 'error');
        return;
    }

    if (height <= 0 || weight <= 0) {
        console.log('Invalid height or weight values');
        showAlert('Please enter valid positive values for height and weight.', 'error');
        return;
    }

    // Send to Django backend
    fetch('/api/calculate-bmi/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken(),
        },
        body: JSON.stringify({
            height: height,
            weight: weight,
            height_unit: heightUnit,
            weight_unit: weightUnit
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const result = data.result;

            // Display results
            const bmiElement = document.getElementById('calculatedBMI');
            const categoryElement = document.getElementById('bmiCategory');

            if (bmiElement && categoryElement) {
                bmiElement.textContent = result.bmi;
                categoryElement.textContent = result.category;
                categoryElement.className = `result-category ${result.category_class}`;
                console.log('BMI results displayed');
            }

            // Highlight the appropriate BMI range
            document.querySelectorAll('.bmi-range').forEach(range => {
                range.classList.remove('active');
            });

            const targetRange = document.querySelector(`.bmi-range.${result.category_class}`);
            if (targetRange) {
                targetRange.classList.add('active');
                console.log('BMI range highlighted:', result.category_class);
            }

            // Show result
            const resultElement = document.getElementById('bmiResult');
            if (resultElement) {
                resultElement.style.display = 'block';
                console.log('BMI result shown');
            }

            // Scroll to result smoothly
            setTimeout(() => {
                document.getElementById('bmiResult').scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 100);

            showAlert('BMI calculated successfully!', 'success');
        } else {
            showAlert(data.error || 'Error calculating BMI', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error calculating BMI. Please try again.', 'error');
    });
}

// Weight Gain Calculator
function initializeWeightGainCalculator() {
    console.log('Initializing Weight Gain Calculator...');
    const form = document.getElementById('weightGainForm');
    if (!form) {
        console.error('Weight Gain form not found!');
        return;
    }

    console.log('Weight Gain form found, adding event listener');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        console.log('Weight Gain form submitted');
        calculateWeightGain();
    });
}

function calculateWeightGain() {
    console.log('Calculating Weight Gain...');

    const weight = parseFloat(document.getElementById('prePregnancyWeight').value);
    const height = parseFloat(document.getElementById('pregnancyHeight').value);
    const pregnancyType = document.getElementById('pregnancyType').value;

    console.log('Weight Gain Input values:', { weight, height, pregnancyType });

    if (!weight || !height || !pregnancyType) {
        console.log('Missing required fields');
        showAlert('Please fill in all fields.', 'error');
        return;
    }

    if (weight <= 0 || height <= 0) {
        console.log('Invalid weight or height values');
        showAlert('Please enter valid positive values for weight and height.', 'error');
        return;
    }

    // Send to Django backend
    fetch('/api/calculate-weight-gain/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCSRFToken(),
        },
        body: JSON.stringify({
            pre_pregnancy_weight: weight,
            height: height,
            pregnancy_type: pregnancyType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const result = data.result;

            // Display results
            const rangeElement = document.getElementById('weightGainRange');
            const bmiElement = document.getElementById('prePregnancyBMI');
            const categoryElement = document.getElementById('prePregnancyCategory');

            if (rangeElement && bmiElement && categoryElement) {
                rangeElement.textContent = result.weight_gain_range;
                bmiElement.textContent = result.pre_pregnancy_bmi;
                categoryElement.textContent = result.bmi_category;
                console.log('Weight Gain results displayed');
            }

            // Show result
            const resultElement = document.getElementById('weightGainResult');
            if (resultElement) {
                resultElement.style.display = 'block';
                console.log('Weight Gain result shown');
            }

            // Scroll to result smoothly
            setTimeout(() => {
                document.getElementById('weightGainResult').scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 100);

            showAlert('Weight gain recommendations calculated successfully!', 'success');
        } else {
            showAlert(data.error || 'Error calculating weight gain', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error calculating weight gain. Please try again.', 'error');
    });
}

// Utility functions
function formatDate(date) {
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    return date.toLocaleDateString('en-US', options);
}

// Add smooth scrolling to tool sections
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's a hash in the URL and scroll to it
    if (window.location.hash) {
        const target = document.querySelector(window.location.hash);
        if (target) {
            setTimeout(() => {
                target.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }, 100);
        }
    }
});

// Alert function for better user feedback
function showAlert(message, type = 'info') {
    // Create alert element
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: 500;
        max-width: 400px;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        background-color: ${type === 'error' ? '#e74c3c' : type === 'warning' ? '#f39c12' : type === 'success' ? '#27ae60' : '#3498db'};
    `;
    alert.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'warning' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: white; font-size: 18px; cursor: pointer; margin-left: auto;">&times;</button>
        </div>
    `;

    // Add to page
    document.body.appendChild(alert);

    // Remove after 5 seconds
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}

// Export functions for global use
window.toolsFunctions = {
    calculateDueDate,
    calculateBMI,
    calculateWeightGain,
    formatDate,
    showAlert
};
