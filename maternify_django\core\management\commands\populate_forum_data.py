from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from core.models import ForumPost, ForumReply
from datetime import datetime, timedelta
import random

User = get_user_model()

class Command(BaseCommand):
    help = 'Populate forum with sample data'

    def handle(self, *args, **options):
        self.stdout.write('Creating sample forum data...')

        # Get or create sample users
        user1, created = User.objects.get_or_create(
            username='demo_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Demo',
                'last_name': 'User',
                'is_pregnant': True,
                'due_date': datetime.now().date() + timedelta(days=120)
            }
        )
        if created:
            user1.set_password('demo123')
            user1.save()

        user2, created = User.objects.get_or_create(
            username='jane_doe',
            defaults={
                'email': '<EMAIL>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON><PERSON>',
                'is_pregnant': False
            }
        )
        if created:
            user2.set_password('password123')
            user2.save()

        # Sample forum posts
        sample_posts = [
            {
                'title': 'First Trimester Tips - What to Expect',
                'content': 'I\'m in my first trimester and looking for advice on nutrition, exercise, and managing morning sickness. What are some essential tips that helped you during this time?',
                'category': 'pregnancy',
                'author': user1,
                'is_anonymous': False,
                'views': 45,
                'likes': 12
            },
            {
                'title': 'Breastfeeding Challenges - Need Support',
                'content': 'I\'m having trouble with breastfeeding and feeling overwhelmed. Any tips for improving latch and increasing milk supply? How long did it take for you to feel comfortable?',
                'category': 'feeding',
                'author': user2,
                'is_anonymous': False,
                'views': 38,
                'likes': 8
            },
            {
                'title': 'Newborn Sleep Schedule Help',
                'content': 'My 2-week-old seems to have day and night confused. Any advice on establishing a good sleep routine? When did your baby start sleeping longer stretches?',
                'category': 'newborn',
                'author': user1,
                'is_anonymous': False,
                'views': 52,
                'likes': 15
            },
            {
                'title': 'Postpartum Anxiety - Is This Normal?',
                'content': 'I\'m experiencing a lot of anxiety since giving birth. Is this normal? When should I seek help? I feel like I\'m not bonding with my baby the way I should.',
                'category': 'support',
                'author': user2,
                'is_anonymous': True,
                'views': 67,
                'likes': 23
            },
            {
                'title': 'Exercise During Pregnancy - Safe Options',
                'content': 'What exercises are safe during pregnancy? I used to run regularly but now I\'m not sure what\'s appropriate. Any recommendations for prenatal fitness?',
                'category': 'health',
                'author': user1,
                'is_anonymous': False,
                'views': 29,
                'likes': 6
            },
            {
                'title': 'Introduction - New Mom Here!',
                'content': 'Hi everyone! I just joined this community and wanted to introduce myself. I\'m a first-time mom with a 3-month-old baby. Looking forward to connecting with other moms!',
                'category': 'general',
                'author': user2,
                'is_anonymous': False,
                'views': 34,
                'likes': 18
            }
        ]

        # Create posts
        created_posts = []
        for post_data in sample_posts:
            post, created = ForumPost.objects.get_or_create(
                title=post_data['title'],
                defaults=post_data
            )
            if created:
                created_posts.append(post)
                self.stdout.write(f'Created post: {post.title}')

        # Sample replies
        if created_posts:
            sample_replies = [
                {
                    'post': created_posts[0],  # First trimester tips
                    'content': 'Great question! For morning sickness, I found that eating small, frequent meals helped a lot. Ginger tea was also a lifesaver for me.',
                    'author': user2,
                    'is_anonymous': False,
                    'likes': 5
                },
                {
                    'post': created_posts[0],
                    'content': 'Thank you for the advice! I\'ll definitely try the ginger tea. Did you have any specific foods that helped or made it worse?',
                    'author': user1,
                    'is_anonymous': False,
                    'likes': 2
                },
                {
                    'post': created_posts[1],  # Breastfeeding challenges
                    'content': 'I had similar challenges! It took about 6 weeks for us to get the hang of it. Don\'t give up - it does get easier. Consider seeing a lactation consultant if you haven\'t already.',
                    'author': user1,
                    'is_anonymous': False,
                    'likes': 7
                },
                {
                    'post': created_posts[3],  # Postpartum anxiety
                    'content': 'You\'re not alone in feeling this way. Postpartum anxiety is very common. Please don\'t hesitate to talk to your doctor about it. There\'s help available and you deserve support.',
                    'author': user1,
                    'is_anonymous': False,
                    'likes': 12
                }
            ]

            # Create replies
            for reply_data in sample_replies:
                reply, created = ForumReply.objects.get_or_create(
                    post=reply_data['post'],
                    content=reply_data['content'],
                    defaults=reply_data
                )
                if created:
                    self.stdout.write(f'Created reply for: {reply.post.title}')

        self.stdout.write(
            self.style.SUCCESS('Successfully populated forum with sample data!')
        )
