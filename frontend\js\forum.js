// Forum JavaScript functionality

let currentCategory = 'all';
let currentReplyPostId = null;

// Sample forum data
const samplePosts = [
    {
        id: 1,
        category: 'pregnancy',
        title: 'First trimester nausea - any tips?',
        content: 'I\'m 8 weeks pregnant and struggling with morning sickness. It\'s actually all-day sickness for me! Has anyone found anything that really helps?',
        author: '<PERSON>',
        anonymous: false,
        timestamp: new Date('2024-03-10T10:30:00'),
        replies: [
            {
                id: 101,
                content: 'Ginger tea really helped me! I also found that eating small, frequent meals made a big difference.',
                author: '<PERSON>',
                anonymous: false,
                timestamp: new Date('2024-03-10T11:15:00')
            },
            {
                id: 102,
                content: 'Try keeping crackers by your bedside and eating a few before getting up. It helped reduce my morning nausea.',
                author: 'Anonymous',
                anonymous: true,
                timestamp: new Date('2024-03-10T14:20:00')
            }
        ],
        likes: 12
    },
    {
        id: 2,
        category: 'newborn',
        title: 'Sleep schedule for 2-month-old?',
        content: 'My baby is 2 months old and I\'m wondering if I should try to establish a sleep schedule or just follow her lead. What worked for you?',
        author: '<PERSON>',
        anonymous: false,
        timestamp: new Date('2024-03-09T15:45:00'),
        replies: [
            {
                id: 201,
                content: 'At 2 months, I think following baby\'s lead is still best. Around 3-4 months you can start gentle routines.',
                author: 'Maria S.',
                anonymous: false,
                timestamp: new Date('2024-03-09T16:30:00')
            }
        ],
        likes: 8
    },
    {
        id: 3,
        category: 'feeding',
        title: 'Breastfeeding in public - confidence tips?',
        content: 'I\'m a new mom and feeling anxious about breastfeeding when we\'re out. Any tips for building confidence?',
        author: 'Anonymous',
        anonymous: true,
        timestamp: new Date('2024-03-08T09:20:00'),
        replies: [
            {
                id: 301,
                content: 'Practice at home with a nursing cover first. It helped me feel more prepared when going out.',
                author: 'Jennifer L.',
                anonymous: false,
                timestamp: new Date('2024-03-08T10:45:00')
            },
            {
                id: 302,
                content: 'Remember that you\'re feeding your baby - that\'s the most natural thing in the world! You\'ve got this!',
                author: 'Rachel T.',
                anonymous: false,
                timestamp: new Date('2024-03-08T12:15:00')
            }
        ],
        likes: 15
    },
    {
        id: 4,
        category: 'health',
        title: 'Postpartum exercise - when to start?',
        content: 'I had a C-section 6 weeks ago and got cleared by my doctor for light exercise. What\'s a good way to start getting back into fitness?',
        author: 'Amanda P.',
        anonymous: false,
        timestamp: new Date('2024-03-07T13:10:00'),
        replies: [
            {
                id: 401,
                content: 'Start with gentle walks and basic stretching. Yoga was great for me - there are specific postpartum classes.',
                author: 'Nicole B.',
                anonymous: false,
                timestamp: new Date('2024-03-07T14:25:00')
            }
        ],
        likes: 6
    },
    {
        id: 5,
        category: 'support',
        title: 'Feeling overwhelmed as a new mom',
        content: 'I love my baby so much, but I\'m feeling really overwhelmed and sometimes sad. Is this normal? When does it get easier?',
        author: 'Anonymous',
        anonymous: true,
        timestamp: new Date('2024-03-06T20:30:00'),
        replies: [
            {
                id: 501,
                content: 'What you\'re feeling is completely normal. The first few months are really tough. It does get easier, I promise. Don\'t hesitate to reach out for help.',
                author: 'Karen M.',
                anonymous: false,
                timestamp: new Date('2024-03-06T21:15:00')
            },
            {
                id: 502,
                content: 'Please talk to your doctor about these feelings. Postpartum depression is real and treatable. You\'re not alone.',
                author: 'Dr. Smith',
                anonymous: false,
                timestamp: new Date('2024-03-07T08:00:00')
            }
        ],
        likes: 23
    },
    {
        id: 6,
        category: 'general',
        title: 'Favorite baby products?',
        content: 'What are your must-have baby products that you couldn\'t live without? I\'m making a list for friends who are expecting.',
        author: 'Michelle D.',
        anonymous: false,
        timestamp: new Date('2024-03-05T11:00:00'),
        replies: [
            {
                id: 601,
                content: 'A good baby carrier was a lifesaver for me! Also, a white noise machine for better sleep.',
                author: 'Stephanie W.',
                anonymous: false,
                timestamp: new Date('2024-03-05T12:30:00')
            }
        ],
        likes: 9
    }
];

// Initialize forum
document.addEventListener('DOMContentLoaded', function() {
    const user = getCurrentUser();
    if (user) {
        document.getElementById('userGreeting').textContent = `Hi, ${user.name || user.fullName || 'User'}!`;
    }
    
    // Load posts from localStorage or use sample data
    loadForumPosts();
    displayPosts();
    
    // Initialize form handlers
    initializeForumForms();
});

function loadForumPosts() {
    const storedPosts = localStorage.getItem('forumPosts');
    if (!storedPosts) {
        localStorage.setItem('forumPosts', JSON.stringify(samplePosts));
    }
}

function getForumPosts() {
    return JSON.parse(localStorage.getItem('forumPosts') || '[]');
}

function saveForumPosts(posts) {
    localStorage.setItem('forumPosts', JSON.stringify(posts));
}

function showCategory(category) {
    currentCategory = category;
    const categoryNames = {
        'pregnancy': 'Pregnancy Journey',
        'newborn': 'Newborn Care',
        'feeding': 'Feeding & Nutrition',
        'health': 'Health & Wellness',
        'support': 'Emotional Support',
        'general': 'General Discussion'
    };
    
    document.getElementById('categoryTitle').textContent = categoryNames[category] || 'Recent Discussions';
    displayPosts();
}

function displayPosts() {
    const posts = getForumPosts();
    const filteredPosts = currentCategory === 'all' 
        ? posts 
        : posts.filter(post => post.category === currentCategory);
    
    const sortedPosts = sortPostsArray(filteredPosts);
    const postsList = document.getElementById('postsList');
    
    if (sortedPosts.length === 0) {
        postsList.innerHTML = `
            <div class="no-posts">
                <i class="fas fa-comments"></i>
                <h3>No posts yet</h3>
                <p>Be the first to start a discussion in this category!</p>
                <button class="btn btn-primary" onclick="showNewPostModal()">Create First Post</button>
            </div>
        `;
        return;
    }
    
    let postsHTML = '';
    sortedPosts.forEach(post => {
        const timeAgo = getTimeAgo(post.timestamp);
        const replyCount = post.replies ? post.replies.length : 0;
        
        postsHTML += `
            <div class="post-card" onclick="expandPost(${post.id})">
                <div class="post-header">
                    <div class="post-author">
                        <i class="fas fa-user-circle"></i>
                        <span>${post.anonymous ? 'Anonymous' : post.author}</span>
                        <span class="post-time">${timeAgo}</span>
                    </div>
                    <div class="post-category">
                        <span class="category-tag category-${post.category}">${getCategoryName(post.category)}</span>
                    </div>
                </div>
                <div class="post-content">
                    <h3 class="post-title">${post.title}</h3>
                    <p class="post-excerpt">${truncateText(post.content, 150)}</p>
                </div>
                <div class="post-footer">
                    <div class="post-stats">
                        <span><i class="fas fa-heart"></i> ${post.likes || 0}</span>
                        <span><i class="fas fa-reply"></i> ${replyCount} replies</span>
                    </div>
                    <div class="post-actions">
                        <button class="action-btn" onclick="event.stopPropagation(); likePost(${post.id})">
                            <i class="fas fa-heart"></i> Like
                        </button>
                        <button class="action-btn" onclick="event.stopPropagation(); showReplyModal(${post.id})">
                            <i class="fas fa-reply"></i> Reply
                        </button>
                    </div>
                </div>
            </div>
        `;
    });
    
    postsList.innerHTML = postsHTML;
}

function sortPostsArray(posts) {
    const sortBy = document.getElementById('sortFilter').value;
    
    switch (sortBy) {
        case 'popular':
            return posts.sort((a, b) => (b.likes || 0) - (a.likes || 0));
        case 'replies':
            return posts.sort((a, b) => (b.replies?.length || 0) - (a.replies?.length || 0));
        case 'recent':
        default:
            return posts.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    }
}

function sortPosts() {
    displayPosts();
}

function getCategoryName(category) {
    const names = {
        'pregnancy': 'Pregnancy',
        'newborn': 'Newborn',
        'feeding': 'Feeding',
        'health': 'Health',
        'support': 'Support',
        'general': 'General'
    };
    return names[category] || category;
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) return text;
    return text.substr(0, maxLength) + '...';
}

function getTimeAgo(timestamp) {
    const now = new Date();
    const postTime = new Date(timestamp);
    const diffInSeconds = Math.floor((now - postTime) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    return `${Math.floor(diffInSeconds / 86400)} days ago`;
}

function initializeForumForms() {
    // New post form
    document.getElementById('newPostForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createNewPost();
    });
    
    // Reply form
    document.getElementById('replyForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createReply();
    });
}

function showNewPostModal() {
    document.getElementById('newPostModal').style.display = 'flex';
    if (currentCategory !== 'all') {
        document.getElementById('postCategory').value = currentCategory;
    }
}

function closeNewPostModal() {
    document.getElementById('newPostModal').style.display = 'none';
    document.getElementById('newPostForm').reset();
}

function createNewPost() {
    const user = getCurrentUser();
    const formData = {
        category: document.getElementById('postCategory').value,
        title: document.getElementById('postTitle').value,
        content: document.getElementById('postContent').value,
        anonymous: document.getElementById('anonymousPost').checked
    };
    
    if (!formData.category || !formData.title || !formData.content) {
        alert('Please fill in all required fields.');
        return;
    }
    
    const posts = getForumPosts();
    const newPost = {
        id: Date.now(),
        category: formData.category,
        title: formData.title,
        content: formData.content,
        author: formData.anonymous ? 'Anonymous' : (user?.name || user?.fullName || 'User'),
        anonymous: formData.anonymous,
        timestamp: new Date().toISOString(),
        replies: [],
        likes: 0
    };
    
    posts.unshift(newPost);
    saveForumPosts(posts);
    
    closeNewPostModal();
    displayPosts();
    
    alert('Your post has been created successfully!');
}

function showReplyModal(postId) {
    currentReplyPostId = postId;
    const posts = getForumPosts();
    const post = posts.find(p => p.id === postId);
    
    if (post) {
        document.getElementById('originalPost').innerHTML = `
            <div class="original-post-content">
                <h4>${post.title}</h4>
                <p><strong>By:</strong> ${post.anonymous ? 'Anonymous' : post.author}</p>
                <p>${post.content}</p>
            </div>
        `;
        document.getElementById('replyModal').style.display = 'flex';
    }
}

function closeReplyModal() {
    document.getElementById('replyModal').style.display = 'none';
    document.getElementById('replyForm').reset();
    currentReplyPostId = null;
}

function createReply() {
    const user = getCurrentUser();
    const replyContent = document.getElementById('replyContent').value;
    const anonymous = document.getElementById('anonymousReply').checked;
    
    if (!replyContent.trim()) {
        alert('Please enter your reply.');
        return;
    }
    
    const posts = getForumPosts();
    const postIndex = posts.findIndex(p => p.id === currentReplyPostId);
    
    if (postIndex !== -1) {
        const newReply = {
            id: Date.now(),
            content: replyContent,
            author: anonymous ? 'Anonymous' : (user?.name || user?.fullName || 'User'),
            anonymous: anonymous,
            timestamp: new Date().toISOString()
        };
        
        if (!posts[postIndex].replies) {
            posts[postIndex].replies = [];
        }
        
        posts[postIndex].replies.push(newReply);
        saveForumPosts(posts);
        
        closeReplyModal();
        displayPosts();
        
        alert('Your reply has been posted successfully!');
    }
}

function likePost(postId) {
    const posts = getForumPosts();
    const postIndex = posts.findIndex(p => p.id === postId);
    
    if (postIndex !== -1) {
        posts[postIndex].likes = (posts[postIndex].likes || 0) + 1;
        saveForumPosts(posts);
        displayPosts();
    }
}

function expandPost(postId) {
    // This would typically open a detailed view of the post
    // For now, we'll just show the reply modal
    showReplyModal(postId);
}

// Close modals when clicking outside
window.addEventListener('click', function(event) {
    const newPostModal = document.getElementById('newPostModal');
    const replyModal = document.getElementById('replyModal');
    
    if (event.target === newPostModal) {
        closeNewPostModal();
    }
    
    if (event.target === replyModal) {
        closeReplyModal();
    }
});
