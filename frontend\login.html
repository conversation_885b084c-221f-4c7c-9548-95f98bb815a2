<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - MomCare</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="auth-container">
        <a href="index.html" class="back-to-home">
            <i class="fas fa-arrow-left"></i>
            Back to Home
        </a>

        <div class="auth-card">
            <div class="auth-logo">
                <i class="fas fa-heart"></i>
                <span>MomCare</span>
            </div>
            
            <h1 class="auth-title">Welcome Back</h1>
            <p class="auth-subtitle">Sign in to continue your journey</p>

            <div id="alert-container"></div>

            <form class="auth-form" id="loginForm">
                <div class="form-group">
                    <label for="email" class="form-label">Email Address</label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        class="form-input" 
                        placeholder="Enter your email"
                        required
                    >
                    <div class="form-error" id="emailError"></div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="password-container">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            placeholder="Enter your password"
                            required
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                    <div class="form-error" id="passwordError"></div>
                </div>

                <button type="submit" class="auth-button" id="loginButton">
                    Sign In
                </button>
            </form>

            <div class="auth-divider">
                <span>or</span>
            </div>

            <div class="auth-link">
                <p>Don't have an account? <a href="register.html">Create one here</a></p>
            </div>

            <div class="auth-link">
                <p><a href="#" onclick="showForgotPassword()">Forgot your password?</a></p>
            </div>
        </div>
    </div>

    <!-- Demo Credentials Modal -->
    <div id="demoModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 2000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 15px; max-width: 400px; width: 90%;">
            <h3 style="margin-bottom: 1rem; color: #333;">Demo Credentials</h3>
            <p style="margin-bottom: 1rem; color: #666;">Use these credentials to test the application:</p>
            <div style="background: #f8f9fa; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> demo123</p>
            </div>
            <div style="display: flex; gap: 1rem; justify-content: flex-end;">
                <button onclick="closeDemoModal()" style="padding: 0.5rem 1rem; background: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">Close</button>
                <button onclick="fillDemoCredentials()" style="padding: 0.5rem 1rem; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">Use Demo</button>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Show demo credentials on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                document.getElementById('demoModal').style.display = 'block';
            }, 1000);
        });

        function closeDemoModal() {
            document.getElementById('demoModal').style.display = 'none';
        }

        function fillDemoCredentials() {
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('password').value = 'demo123';
            closeDemoModal();
        }

        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + 'ToggleIcon');
            
            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                field.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        function showForgotPassword() {
            alert('Password reset functionality would be implemented here. For demo purposes, use the demo credentials provided.');
        }

        // Login form handling
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            // Clear previous errors
            clearErrors();
            
            // Validate form
            if (!validateLoginForm(email, password)) {
                return;
            }
            
            // Show loading state
            const button = document.getElementById('loginButton');
            const hideLoading = showLoading(button);
            
            // Simulate API call
            setTimeout(() => {
                hideLoading();
                
                // Demo authentication
                if (email === '<EMAIL>' && password === 'demo123') {
                    // Store user session
                    localStorage.setItem('userLoggedIn', 'true');
                    localStorage.setItem('currentUser', JSON.stringify({
                        name: 'Demo User',
                        email: email,
                        loginTime: new Date().toISOString()
                    }));
                    
                    showAlert('Login successful! Redirecting...', 'success');
                    
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 1500);
                } else {
                    showAlert('Invalid email or password. Please use the demo credentials.', 'error');
                }
            }, 1500);
        });

        function validateLoginForm(email, password) {
            let isValid = true;
            
            if (!email) {
                showFieldError('emailError', 'Email is required');
                isValid = false;
            } else if (!validateEmail(email)) {
                showFieldError('emailError', 'Please enter a valid email address');
                isValid = false;
            }
            
            if (!password) {
                showFieldError('passwordError', 'Password is required');
                isValid = false;
            }
            
            return isValid;
        }

        function showFieldError(fieldId, message) {
            const errorElement = document.getElementById(fieldId);
            errorElement.textContent = message;
            errorElement.classList.add('show');
            
            const inputField = errorElement.previousElementSibling;
            if (inputField.classList.contains('password-container')) {
                inputField.querySelector('input').classList.add('error');
            } else {
                inputField.classList.add('error');
            }
        }

        function clearErrors() {
            document.querySelectorAll('.form-error').forEach(error => {
                error.classList.remove('show');
                error.textContent = '';
            });
            
            document.querySelectorAll('.form-input').forEach(input => {
                input.classList.remove('error', 'success');
            });
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            alertContainer.innerHTML = `
                <div class="alert alert-${type}">
                    <span>${message}</span>
                    <button class="alert-close" onclick="this.parentElement.remove()">&times;</button>
                </div>
            `;
        }
    </script>
</body>
</html>
