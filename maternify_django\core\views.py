from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.generic import ListView, DetailView
from .models import Article, ForumPost, Resource
from accounts.models import HealthCalculation
import json
from datetime import datetime, date, timedelta


def index_view(request):
    """Home page view"""
    featured_articles = Article.objects.filter(is_published=True)[:3]
    recent_posts = ForumPost.objects.all()[:5]
    featured_resources = Resource.objects.filter(is_featured=True)[:4]

    context = {
        'featured_articles': featured_articles,
        'recent_posts': recent_posts,
        'featured_resources': featured_resources,
    }
    return render(request, 'core/index.html', context)


def about_view(request):
    """About page view"""
    return render(request, 'core/about.html')


@login_required
def dashboard_view(request):
    """Dashboard view"""
    user = request.user

    # Get user's recent data
    recent_calculations = user.calculations.all()[:5]
    upcoming_appointments = user.appointments.filter(
        appointment_date__gte=datetime.now(),
        is_completed=False
    )[:3]
    recent_posts = ForumPost.objects.all()[:5]

    # Calculate pregnancy week if applicable
    pregnancy_week = None
    if user.is_pregnant and user.due_date:
        today = date.today()
        days_until_due = (user.due_date - today).days
        pregnancy_week = max(0, 40 - (days_until_due // 7))

    context = {
        'user': user,
        'pregnancy_week': pregnancy_week,
        'recent_calculations': recent_calculations,
        'upcoming_appointments': upcoming_appointments,
        'recent_posts': recent_posts,
    }
    return render(request, 'core/dashboard.html', context)


def pregnancy_care_view(request):
    """Pregnancy care page"""
    articles = Article.objects.filter(category='pregnancy', is_published=True)
    resources = Resource.objects.filter(category='pregnancy')

    context = {
        'articles': articles,
        'resources': resources,
    }
    return render(request, 'core/pregnancy_care.html', context)


def infant_care_view(request):
    """Infant care page"""
    articles = Article.objects.filter(category='infant', is_published=True)
    resources = Resource.objects.filter(category='infant')

    context = {
        'articles': articles,
        'resources': resources,
    }
    return render(request, 'core/infant_care.html', context)


@login_required
def tools_view(request):
    """Health tools page"""
    return render(request, 'core/tools.html')


@login_required
@csrf_exempt
def save_calculation(request):
    """Save health calculation results"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            calculation_type = data.get('type')
            input_data = data.get('input_data')
            result_data = data.get('result_data')

            HealthCalculation.objects.create(
                user=request.user,
                calculation_type=calculation_type,
                input_data=input_data,
                result_data=result_data
            )

            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
@csrf_exempt
def calculate_due_date(request):
    """Calculate due date from LMP"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            lmp_date = data.get('lmp_date')

            if not lmp_date:
                return JsonResponse({'success': False, 'error': 'LMP date is required'})

            # Parse LMP date
            lmp = datetime.strptime(lmp_date, '%Y-%m-%d').date()

            # Calculate due date (280 days from LMP)
            due_date = lmp + timedelta(days=280)

            # Calculate current pregnancy week
            today = date.today()
            days_pregnant = (today - lmp).days
            weeks_pregnant = days_pregnant // 7

            # Calculate days remaining
            days_remaining = (due_date - today).days

            # Determine trimester
            if weeks_pregnant <= 12:
                trimester = "1st"
            elif weeks_pregnant <= 27:
                trimester = "2nd"
            else:
                trimester = "3rd"

            result_data = {
                'due_date': due_date.strftime('%Y-%m-%d'),
                'due_date_formatted': due_date.strftime('%B %d, %Y'),
                'weeks_pregnant': max(0, weeks_pregnant),
                'days_remaining': max(0, days_remaining),
                'trimester': trimester if weeks_pregnant > 0 else 'Not yet pregnant'
            }

            # Save calculation
            HealthCalculation.objects.create(
                user=request.user,
                calculation_type='due_date',
                input_data={'lmp_date': lmp_date},
                result_data=result_data
            )

            return JsonResponse({'success': True, 'result': result_data})

        except ValueError:
            return JsonResponse({'success': False, 'error': 'Invalid date format'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
@csrf_exempt
def calculate_bmi(request):
    """Enhanced BMI Calculator with age, activity level, and personalized recommendations"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            height = float(data.get('height', 0))
            weight = float(data.get('weight', 0))
            age = int(data.get('age', 25))
            height_unit = data.get('height_unit', 'cm')
            weight_unit = data.get('weight_unit', 'kg')
            activity_level = data.get('activity_level', 'moderate')
            is_pregnant = data.get('pregnancy_status', False)

            if height <= 0 or weight <= 0:
                return JsonResponse({'success': False, 'error': 'Height and weight must be positive values'})

            # Convert to metric units
            if height_unit == 'ft':
                height = height * 30.48  # Convert feet to cm

            if weight_unit == 'lbs':
                weight = weight * 0.453592  # Convert lbs to kg

            # Convert height to meters
            height_m = height / 100

            # Calculate BMI
            bmi = weight / (height_m ** 2)

            # Determine category
            if bmi < 18.5:
                category = "Underweight"
                category_class = "underweight"
            elif bmi < 25:
                category = "Normal weight"
                category_class = "normal"
            elif bmi < 30:
                category = "Overweight"
                category_class = "overweight"
            else:
                category = "Obese"
                category_class = "obese"

            # Calculate ideal weight range (BMI 18.5-24.9)
            ideal_weight_min = 18.5 * (height_m ** 2)
            ideal_weight_max = 24.9 * (height_m ** 2)
            ideal_weight_range = f"{ideal_weight_min:.1f} - {ideal_weight_max:.1f} kg"

            # Calculate daily calorie needs using Mifflin-St Jeor Equation
            # For women: BMR = 10 × weight(kg) + 6.25 × height(cm) - 5 × age(years) - 161
            # For men: BMR = 10 × weight(kg) + 6.25 × height(cm) - 5 × age(years) + 5
            # Assuming female for pregnancy app
            bmr = 10 * weight + 6.25 * height - 5 * age - 161

            # Activity multipliers
            activity_multipliers = {
                'sedentary': 1.2,
                'light': 1.375,
                'moderate': 1.55,
                'active': 1.725,
                'very_active': 1.9
            }

            daily_calories = int(bmr * activity_multipliers.get(activity_level, 1.55))

            # Adjust for pregnancy
            if is_pregnant:
                daily_calories += 300  # Additional calories during pregnancy

            # Generate personalized recommendations
            recommendations = []
            if bmi < 18.5:
                recommendations.append("Consider consulting a healthcare provider about healthy weight gain strategies.")
                recommendations.append("Focus on nutrient-dense foods to increase caloric intake.")
            elif bmi >= 25:
                recommendations.append("Consider a balanced diet and regular exercise for healthy weight management.")
                recommendations.append("Consult with a healthcare provider for personalized weight management advice.")
            else:
                recommendations.append("Maintain your current healthy weight with balanced nutrition and regular exercise.")

            if is_pregnant:
                recommendations.append("Follow your healthcare provider's guidance for healthy pregnancy weight gain.")

            result_data = {
                'bmi': round(bmi, 1),
                'category': category,
                'category_class': category_class,
                'height_cm': round(height, 1),
                'weight_kg': round(weight, 1),
                'ideal_weight_range': ideal_weight_range,
                'daily_calories': daily_calories,
                'activity_level': activity_level,
                'age': age,
                'is_pregnant': is_pregnant,
                'recommendations': recommendations
            }

            # Save calculation
            HealthCalculation.objects.create(
                user=request.user,
                calculation_type='bmi',
                input_data={
                    'height': data.get('height'),
                    'weight': data.get('weight'),
                    'age': age,
                    'height_unit': height_unit,
                    'weight_unit': weight_unit,
                    'activity_level': activity_level,
                    'pregnancy_status': is_pregnant
                },
                result_data=result_data
            )

            return JsonResponse({'success': True, 'result': result_data})

        except (ValueError, TypeError):
            return JsonResponse({'success': False, 'error': 'Invalid input values'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
@csrf_exempt
def calculate_weight_gain(request):
    """Calculate pregnancy weight gain recommendations"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            pre_pregnancy_weight = float(data.get('pre_pregnancy_weight', 0))
            height = float(data.get('height', 0))
            pregnancy_type = data.get('pregnancy_type', 'single')

            if pre_pregnancy_weight <= 0 or height <= 0:
                return JsonResponse({'success': False, 'error': 'Weight and height must be positive values'})

            # Calculate pre-pregnancy BMI
            height_m = height / 100  # Convert cm to meters
            pre_pregnancy_bmi = pre_pregnancy_weight / (height_m ** 2)

            # Determine BMI category
            if pre_pregnancy_bmi < 18.5:
                bmi_category = "Underweight"
                if pregnancy_type == 'single':
                    weight_gain_range = "12.5-18 kg"
                elif pregnancy_type == 'twins':
                    weight_gain_range = "No established guidelines"
                else:
                    weight_gain_range = "No established guidelines"
            elif pre_pregnancy_bmi < 25:
                bmi_category = "Normal weight"
                if pregnancy_type == 'single':
                    weight_gain_range = "11.5-16 kg"
                elif pregnancy_type == 'twins':
                    weight_gain_range = "17-25 kg"
                else:
                    weight_gain_range = "No established guidelines"
            elif pre_pregnancy_bmi < 30:
                bmi_category = "Overweight"
                if pregnancy_type == 'single':
                    weight_gain_range = "7-11.5 kg"
                elif pregnancy_type == 'twins':
                    weight_gain_range = "14-23 kg"
                else:
                    weight_gain_range = "No established guidelines"
            else:
                bmi_category = "Obese"
                if pregnancy_type == 'single':
                    weight_gain_range = "5-9 kg"
                elif pregnancy_type == 'twins':
                    weight_gain_range = "11-19 kg"
                else:
                    weight_gain_range = "No established guidelines"

            result_data = {
                'pre_pregnancy_bmi': round(pre_pregnancy_bmi, 1),
                'bmi_category': bmi_category,
                'weight_gain_range': weight_gain_range,
                'pregnancy_type': pregnancy_type.title()
            }

            # Save calculation
            HealthCalculation.objects.create(
                user=request.user,
                calculation_type='weight_gain',
                input_data={
                    'pre_pregnancy_weight': pre_pregnancy_weight,
                    'height': height,
                    'pregnancy_type': pregnancy_type
                },
                result_data=result_data
            )

            return JsonResponse({'success': True, 'result': result_data})

        except (ValueError, TypeError):
            return JsonResponse({'success': False, 'error': 'Invalid weight or height values'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
@csrf_exempt
def track_blood_pressure(request):
    """Track blood pressure readings"""
    if request.method == 'POST':
        try:
            from accounts.models import HealthMetric
            data = json.loads(request.body)
            systolic = int(data.get('systolic', 0))
            diastolic = int(data.get('diastolic', 0))
            heart_rate = data.get('heart_rate')
            measurement_time = data.get('measurement_time', 'morning')

            if systolic <= 0 or diastolic <= 0:
                return JsonResponse({'success': False, 'error': 'Invalid blood pressure values'})

            # Determine blood pressure category
            if systolic < 120 and diastolic < 80:
                category = "Normal"
                category_class = "normal"
                recommendation = "Your blood pressure is in the normal range. Keep up the healthy lifestyle!"
            elif systolic < 130 and diastolic < 80:
                category = "Elevated"
                category_class = "elevated"
                recommendation = "Your blood pressure is elevated. Consider lifestyle changes and monitor regularly."
            elif systolic < 140 or diastolic < 90:
                category = "High Blood Pressure Stage 1"
                category_class = "high"
                recommendation = "You have Stage 1 high blood pressure. Consult with your healthcare provider."
            elif systolic < 180 or diastolic < 120:
                category = "High Blood Pressure Stage 2"
                category_class = "high"
                recommendation = "You have Stage 2 high blood pressure. Seek medical attention promptly."
            else:
                category = "Hypertensive Crisis"
                category_class = "crisis"
                recommendation = "This is a hypertensive crisis. Seek emergency medical care immediately!"

            # Save blood pressure reading
            HealthMetric.objects.create(
                user=request.user,
                metric_type='blood_pressure',
                value=systolic,  # Store systolic as main value
                unit='mmHg',
                notes=f"Systolic: {systolic}, Diastolic: {diastolic}, Time: {measurement_time}"
            )

            # Save heart rate if provided
            if heart_rate:
                HealthMetric.objects.create(
                    user=request.user,
                    metric_type='heart_rate',
                    value=float(heart_rate),
                    unit='bpm'
                )

            result_data = {
                'systolic': systolic,
                'diastolic': diastolic,
                'heart_rate': heart_rate,
                'category': category,
                'category_class': category_class,
                'recommendation': recommendation,
                'measurement_time': measurement_time
            }

            # Save calculation
            HealthCalculation.objects.create(
                user=request.user,
                calculation_type='blood_pressure',
                input_data={
                    'systolic': systolic,
                    'diastolic': diastolic,
                    'heart_rate': heart_rate,
                    'measurement_time': measurement_time
                },
                result_data=result_data
            )

            return JsonResponse({'success': True, 'result': result_data})

        except (ValueError, TypeError):
            return JsonResponse({'success': False, 'error': 'Invalid input values'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
@csrf_exempt
def calculate_nutrition(request):
    """Calculate daily nutrition needs"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            age = int(data.get('age', 25))
            gender = data.get('gender', 'female')
            height = float(data.get('height', 0))
            weight = float(data.get('weight', 0))
            activity_level = data.get('activity_level', 'moderate')
            goal = data.get('goal', 'maintain')

            if height <= 0 or weight <= 0:
                return JsonResponse({'success': False, 'error': 'Height and weight must be positive values'})

            # Calculate BMR using Mifflin-St Jeor Equation
            if gender == 'female':
                bmr = 10 * weight + 6.25 * height - 5 * age - 161
            else:
                bmr = 10 * weight + 6.25 * height - 5 * age + 5

            # Activity multipliers
            activity_multipliers = {
                'sedentary': 1.2,
                'light': 1.375,
                'moderate': 1.55,
                'active': 1.725,
                'very_active': 1.9
            }

            # Calculate daily calories
            daily_calories = int(bmr * activity_multipliers.get(activity_level, 1.55))

            # Adjust based on goal
            if goal == 'lose':
                daily_calories -= 500  # 500 calorie deficit for weight loss
            elif goal == 'gain':
                daily_calories += 500  # 500 calorie surplus for weight gain
            elif goal == 'pregnancy':
                daily_calories += 300  # Additional calories for pregnancy

            # Calculate macronutrients
            protein_grams = round(weight * 1.2)  # 1.2g per kg body weight
            fat_grams = round(daily_calories * 0.25 / 9)  # 25% of calories from fat
            carb_grams = round((daily_calories - (protein_grams * 4) - (fat_grams * 9)) / 4)

            # Calculate water needs (35ml per kg body weight)
            water_liters = round(weight * 0.035, 1)

            # Fiber recommendation
            fiber_grams = 25 if gender == 'female' else 38

            result_data = {
                'daily_calories': daily_calories,
                'protein_grams': protein_grams,
                'carb_grams': carb_grams,
                'fat_grams': fat_grams,
                'water_liters': water_liters,
                'fiber_grams': fiber_grams,
                'goal': goal,
                'activity_level': activity_level
            }

            # Save calculation
            HealthCalculation.objects.create(
                user=request.user,
                calculation_type='nutrition',
                input_data={
                    'age': age,
                    'gender': gender,
                    'height': height,
                    'weight': weight,
                    'activity_level': activity_level,
                    'goal': goal
                },
                result_data=result_data
            )

            return JsonResponse({'success': True, 'result': result_data})

        except (ValueError, TypeError):
            return JsonResponse({'success': False, 'error': 'Invalid input values'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
@csrf_exempt
def track_water_intake(request):
    """Track daily water intake"""
    if request.method == 'POST':
        try:
            from accounts.models import HealthMetric, NutritionLog
            from datetime import date

            data = json.loads(request.body)
            water_amount = float(data.get('water_amount', 0))
            water_unit = data.get('water_unit', 'ml')
            target = float(data.get('target', 2.5))

            if water_amount <= 0:
                return JsonResponse({'success': False, 'error': 'Water amount must be positive'})

            # Convert to liters
            if water_unit == 'ml':
                water_liters = water_amount / 1000
            elif water_unit == 'oz':
                water_liters = water_amount * 0.0295735  # fl oz to liters
            elif water_unit == 'cups':
                water_liters = water_amount * 0.236588  # cups to liters
            else:
                water_liters = water_amount

            # Get or create today's nutrition log
            today = date.today()
            nutrition_log, created = NutritionLog.objects.get_or_create(
                user=request.user,
                date=today,
                defaults={'water_target': target}
            )

            # Add to today's water intake
            nutrition_log.water_intake += water_liters
            nutrition_log.save()

            # Calculate progress
            progress_percentage = min(100, (nutrition_log.water_intake / target) * 100)
            remaining = max(0, target - nutrition_log.water_intake)

            result_data = {
                'water_consumed': round(nutrition_log.water_intake, 2),
                'water_target': target,
                'water_remaining': round(remaining, 2),
                'progress_percentage': round(progress_percentage, 1),
                'amount_added': round(water_liters, 2)
            }

            # Save calculation
            HealthCalculation.objects.create(
                user=request.user,
                calculation_type='water_intake',
                input_data={
                    'water_amount': water_amount,
                    'water_unit': water_unit,
                    'target': target
                },
                result_data=result_data
            )

            return JsonResponse({'success': True, 'result': result_data})

        except (ValueError, TypeError):
            return JsonResponse({'success': False, 'error': 'Invalid input values'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


class ForumListView(ListView):
    """Forum posts list view"""
    model = ForumPost
    template_name = 'core/forum.html'
    context_object_name = 'posts'
    paginate_by = 10

    def get_queryset(self):
        return ForumPost.objects.all()


class ForumDetailView(DetailView):
    """Forum post detail view"""
    model = ForumPost
    template_name = 'core/forum_detail.html'
    context_object_name = 'post'

    def get_object(self):
        obj = super().get_object()
        obj.views += 1
        obj.save()
        return obj


class ResourceListView(ListView):
    """Resources list view"""
    model = Resource
    template_name = 'core/resources.html'
    context_object_name = 'resources'
    paginate_by = 12

    def get_queryset(self):
        category = self.request.GET.get('category')
        queryset = Resource.objects.all()
        if category:
            queryset = queryset.filter(category=category)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Resource.RESOURCE_TYPES
        context['selected_category'] = self.request.GET.get('category', '')
        return context
