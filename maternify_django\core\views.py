from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.generic import ListView, DetailView
from .models import Article, ForumPost, Resource
from accounts.models import HealthCalculation
import json
from datetime import datetime, date, timedelta


def index_view(request):
    """Home page view"""
    featured_articles = Article.objects.filter(is_published=True)[:3]
    recent_posts = ForumPost.objects.all()[:5]
    featured_resources = Resource.objects.filter(is_featured=True)[:4]

    context = {
        'featured_articles': featured_articles,
        'recent_posts': recent_posts,
        'featured_resources': featured_resources,
    }
    return render(request, 'core/index.html', context)


def about_view(request):
    """About page view"""
    return render(request, 'core/about.html')


@login_required
def dashboard_view(request):
    """Dashboard view"""
    user = request.user

    # Get user's recent data
    recent_calculations = user.calculations.all()[:5]
    upcoming_appointments = user.appointments.filter(
        appointment_date__gte=datetime.now(),
        is_completed=False
    )[:3]
    recent_posts = ForumPost.objects.all()[:5]

    # Calculate pregnancy week if applicable
    pregnancy_week = None
    if user.is_pregnant and user.due_date:
        today = date.today()
        days_until_due = (user.due_date - today).days
        pregnancy_week = max(0, 40 - (days_until_due // 7))

    context = {
        'user': user,
        'pregnancy_week': pregnancy_week,
        'recent_calculations': recent_calculations,
        'upcoming_appointments': upcoming_appointments,
        'recent_posts': recent_posts,
    }
    return render(request, 'core/dashboard.html', context)


def pregnancy_care_view(request):
    """Pregnancy care page"""
    articles = Article.objects.filter(category='pregnancy', is_published=True)
    resources = Resource.objects.filter(category='pregnancy')

    context = {
        'articles': articles,
        'resources': resources,
    }
    return render(request, 'core/pregnancy_care.html', context)


def infant_care_view(request):
    """Infant care page"""
    articles = Article.objects.filter(category='infant', is_published=True)
    resources = Resource.objects.filter(category='infant')

    context = {
        'articles': articles,
        'resources': resources,
    }
    return render(request, 'core/infant_care.html', context)


@login_required
def tools_view(request):
    """Health tools page"""
    return render(request, 'core/tools.html')


@login_required
@csrf_exempt
def save_calculation(request):
    """Save health calculation results"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            calculation_type = data.get('type')
            input_data = data.get('input_data')
            result_data = data.get('result_data')

            HealthCalculation.objects.create(
                user=request.user,
                calculation_type=calculation_type,
                input_data=input_data,
                result_data=result_data
            )

            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
@csrf_exempt
def calculate_due_date(request):
    """Calculate due date from LMP"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            lmp_date = data.get('lmp_date')

            if not lmp_date:
                return JsonResponse({'success': False, 'error': 'LMP date is required'})

            # Parse LMP date
            lmp = datetime.strptime(lmp_date, '%Y-%m-%d').date()

            # Calculate due date (280 days from LMP)
            due_date = lmp + timedelta(days=280)

            # Calculate current pregnancy week
            today = date.today()
            days_pregnant = (today - lmp).days
            weeks_pregnant = days_pregnant // 7

            # Calculate days remaining
            days_remaining = (due_date - today).days

            # Determine trimester
            if weeks_pregnant <= 12:
                trimester = "1st"
            elif weeks_pregnant <= 27:
                trimester = "2nd"
            else:
                trimester = "3rd"

            result_data = {
                'due_date': due_date.strftime('%Y-%m-%d'),
                'due_date_formatted': due_date.strftime('%B %d, %Y'),
                'weeks_pregnant': max(0, weeks_pregnant),
                'days_remaining': max(0, days_remaining),
                'trimester': trimester if weeks_pregnant > 0 else 'Not yet pregnant'
            }

            # Save calculation
            HealthCalculation.objects.create(
                user=request.user,
                calculation_type='due_date',
                input_data={'lmp_date': lmp_date},
                result_data=result_data
            )

            return JsonResponse({'success': True, 'result': result_data})

        except ValueError:
            return JsonResponse({'success': False, 'error': 'Invalid date format'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
@csrf_exempt
def calculate_bmi(request):
    """Calculate BMI"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            height = float(data.get('height', 0))
            weight = float(data.get('weight', 0))
            height_unit = data.get('height_unit', 'cm')
            weight_unit = data.get('weight_unit', 'kg')

            if height <= 0 or weight <= 0:
                return JsonResponse({'success': False, 'error': 'Height and weight must be positive values'})

            # Convert to metric units
            if height_unit == 'ft':
                height = height * 30.48  # Convert feet to cm

            if weight_unit == 'lbs':
                weight = weight * 0.453592  # Convert lbs to kg

            # Convert height to meters
            height_m = height / 100

            # Calculate BMI
            bmi = weight / (height_m ** 2)

            # Determine category
            if bmi < 18.5:
                category = "Underweight"
                category_class = "underweight"
            elif bmi < 25:
                category = "Normal weight"
                category_class = "normal"
            elif bmi < 30:
                category = "Overweight"
                category_class = "overweight"
            else:
                category = "Obese"
                category_class = "obese"

            result_data = {
                'bmi': round(bmi, 1),
                'category': category,
                'category_class': category_class,
                'height_cm': round(height, 1),
                'weight_kg': round(weight, 1)
            }

            # Save calculation
            HealthCalculation.objects.create(
                user=request.user,
                calculation_type='bmi',
                input_data={
                    'height': data.get('height'),
                    'weight': data.get('weight'),
                    'height_unit': height_unit,
                    'weight_unit': weight_unit
                },
                result_data=result_data
            )

            return JsonResponse({'success': True, 'result': result_data})

        except (ValueError, TypeError):
            return JsonResponse({'success': False, 'error': 'Invalid height or weight values'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


@login_required
@csrf_exempt
def calculate_weight_gain(request):
    """Calculate pregnancy weight gain recommendations"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            pre_pregnancy_weight = float(data.get('pre_pregnancy_weight', 0))
            height = float(data.get('height', 0))
            pregnancy_type = data.get('pregnancy_type', 'single')

            if pre_pregnancy_weight <= 0 or height <= 0:
                return JsonResponse({'success': False, 'error': 'Weight and height must be positive values'})

            # Calculate pre-pregnancy BMI
            height_m = height / 100  # Convert cm to meters
            pre_pregnancy_bmi = pre_pregnancy_weight / (height_m ** 2)

            # Determine BMI category
            if pre_pregnancy_bmi < 18.5:
                bmi_category = "Underweight"
                if pregnancy_type == 'single':
                    weight_gain_range = "12.5-18 kg"
                elif pregnancy_type == 'twins':
                    weight_gain_range = "No established guidelines"
                else:
                    weight_gain_range = "No established guidelines"
            elif pre_pregnancy_bmi < 25:
                bmi_category = "Normal weight"
                if pregnancy_type == 'single':
                    weight_gain_range = "11.5-16 kg"
                elif pregnancy_type == 'twins':
                    weight_gain_range = "17-25 kg"
                else:
                    weight_gain_range = "No established guidelines"
            elif pre_pregnancy_bmi < 30:
                bmi_category = "Overweight"
                if pregnancy_type == 'single':
                    weight_gain_range = "7-11.5 kg"
                elif pregnancy_type == 'twins':
                    weight_gain_range = "14-23 kg"
                else:
                    weight_gain_range = "No established guidelines"
            else:
                bmi_category = "Obese"
                if pregnancy_type == 'single':
                    weight_gain_range = "5-9 kg"
                elif pregnancy_type == 'twins':
                    weight_gain_range = "11-19 kg"
                else:
                    weight_gain_range = "No established guidelines"

            result_data = {
                'pre_pregnancy_bmi': round(pre_pregnancy_bmi, 1),
                'bmi_category': bmi_category,
                'weight_gain_range': weight_gain_range,
                'pregnancy_type': pregnancy_type.title()
            }

            # Save calculation
            HealthCalculation.objects.create(
                user=request.user,
                calculation_type='weight_gain',
                input_data={
                    'pre_pregnancy_weight': pre_pregnancy_weight,
                    'height': height,
                    'pregnancy_type': pregnancy_type
                },
                result_data=result_data
            )

            return JsonResponse({'success': True, 'result': result_data})

        except (ValueError, TypeError):
            return JsonResponse({'success': False, 'error': 'Invalid weight or height values'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


class ForumListView(ListView):
    """Forum posts list view"""
    model = ForumPost
    template_name = 'core/forum.html'
    context_object_name = 'posts'
    paginate_by = 10

    def get_queryset(self):
        return ForumPost.objects.all()


class ForumDetailView(DetailView):
    """Forum post detail view"""
    model = ForumPost
    template_name = 'core/forum_detail.html'
    context_object_name = 'post'

    def get_object(self):
        obj = super().get_object()
        obj.views += 1
        obj.save()
        return obj


class ResourceListView(ListView):
    """Resources list view"""
    model = Resource
    template_name = 'core/resources.html'
    context_object_name = 'resources'
    paginate_by = 12

    def get_queryset(self):
        category = self.request.GET.get('category')
        queryset = Resource.objects.all()
        if category:
            queryset = queryset.filter(category=category)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Resource.RESOURCE_TYPES
        context['selected_category'] = self.request.GET.get('category', '')
        return context
