from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import ListView, DetailView
from .models import Article, ForumPost, ForumReply, Resource, Appointment
from accounts.models import HealthCalculation
import json
from datetime import datetime, date


def index_view(request):
    """Home page view"""
    featured_articles = Article.objects.filter(is_published=True)[:3]
    recent_posts = ForumPost.objects.all()[:5]
    featured_resources = Resource.objects.filter(is_featured=True)[:4]
    
    context = {
        'featured_articles': featured_articles,
        'recent_posts': recent_posts,
        'featured_resources': featured_resources,
    }
    return render(request, 'core/index.html', context)


def about_view(request):
    """About page view"""
    return render(request, 'core/about.html')


@login_required
def dashboard_view(request):
    """Dashboard view"""
    user = request.user
    
    # Get user's recent data
    recent_calculations = user.calculations.all()[:5]
    upcoming_appointments = user.appointments.filter(
        appointment_date__gte=datetime.now(),
        is_completed=False
    )[:3]
    recent_posts = ForumPost.objects.all()[:5]
    
    # Calculate pregnancy week if applicable
    pregnancy_week = None
    if user.is_pregnant and user.due_date:
        today = date.today()
        days_until_due = (user.due_date - today).days
        pregnancy_week = max(0, 40 - (days_until_due // 7))
    
    context = {
        'user': user,
        'pregnancy_week': pregnancy_week,
        'recent_calculations': recent_calculations,
        'upcoming_appointments': upcoming_appointments,
        'recent_posts': recent_posts,
    }
    return render(request, 'core/dashboard.html', context)


def pregnancy_care_view(request):
    """Pregnancy care page"""
    articles = Article.objects.filter(category='pregnancy', is_published=True)
    resources = Resource.objects.filter(category='pregnancy')
    
    context = {
        'articles': articles,
        'resources': resources,
    }
    return render(request, 'core/pregnancy_care.html', context)


def infant_care_view(request):
    """Infant care page"""
    articles = Article.objects.filter(category='infant', is_published=True)
    resources = Resource.objects.filter(category='infant')
    
    context = {
        'articles': articles,
        'resources': resources,
    }
    return render(request, 'core/infant_care.html', context)


@login_required
def tools_view(request):
    """Health tools page"""
    return render(request, 'core/tools.html')


@login_required
@csrf_exempt
def save_calculation(request):
    """Save health calculation results"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            calculation_type = data.get('type')
            input_data = data.get('input_data')
            result_data = data.get('result_data')
            
            HealthCalculation.objects.create(
                user=request.user,
                calculation_type=calculation_type,
                input_data=input_data,
                result_data=result_data
            )
            
            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})
    
    return JsonResponse({'success': False, 'error': 'Invalid request method'})


class ForumListView(ListView):
    """Forum posts list view"""
    model = ForumPost
    template_name = 'core/forum.html'
    context_object_name = 'posts'
    paginate_by = 10
    
    def get_queryset(self):
        return ForumPost.objects.all()


class ForumDetailView(DetailView):
    """Forum post detail view"""
    model = ForumPost
    template_name = 'core/forum_detail.html'
    context_object_name = 'post'
    
    def get_object(self):
        obj = super().get_object()
        obj.views += 1
        obj.save()
        return obj


class ResourceListView(ListView):
    """Resources list view"""
    model = Resource
    template_name = 'core/resources.html'
    context_object_name = 'resources'
    paginate_by = 12
    
    def get_queryset(self):
        category = self.request.GET.get('category')
        queryset = Resource.objects.all()
        if category:
            queryset = queryset.filter(category=category)
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Resource.RESOURCE_TYPES
        context['selected_category'] = self.request.GET.get('category', '')
        return context
