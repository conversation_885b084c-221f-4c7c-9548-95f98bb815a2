from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    path('', views.index_view, name='index'),
    path('about/', views.about_view, name='about'),
    path('dashboard/', views.dashboard_view, name='dashboard'),
    path('pregnancy-care/', views.pregnancy_care_view, name='pregnancy_care'),
    path('infant-care/', views.infant_care_view, name='infant_care'),
    path('tools/', views.tools_view, name='tools'),
    path('forum/', views.ForumListView.as_view(), name='forum'),
    path('forum/<int:pk>/', views.ForumDetailView.as_view(), name='forum_detail'),
    path('resources/', views.ResourceListView.as_view(), name='resources'),
    path('api/save-calculation/', views.save_calculation, name='save_calculation'),
]
