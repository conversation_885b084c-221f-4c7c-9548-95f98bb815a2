<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infant Care Guide - MATERNIFY</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/logo.svg" alt="MATERNIFY Logo">
                <span>MATERNIFY</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a href="pregnancy-care.html" class="nav-link">Pregnancy Care</a>
                </li>
                <li class="nav-item">
                    <a href="infant-care.html" class="nav-link active">Infant Care</a>
                </li>
                <li class="nav-item">
                    <a href="tools.html" class="nav-link">Tools</a>
                </li>
                <li class="nav-item">
                    <a href="forum.html" class="nav-link">Forum</a>
                </li>
                <li class="nav-item">
                    <a href="resources.html" class="nav-link">Resources</a>
                </li>
                <li class="nav-item">
                    <span class="nav-link user-greeting" id="userGreeting">Hi, User!</span>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link logout-btn" onclick="logout()">Logout</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Infant Care Content -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Page Header -->
            <section class="page-header">
                <h1>Infant Care Guide</h1>
                <p>Essential information for caring for your newborn baby</p>
            </section>

            <!-- Care Categories Navigation -->
            <section class="infant-nav">
                <div class="nav-tabs">
                    <button class="tab-btn active" onclick="showInfantSection('guidance')">
                        <i class="fas fa-baby"></i>
                        Infant Care Guidance
                    </button>
                    <button class="tab-btn" onclick="showInfantSection('feeding')">
                        <i class="fas fa-baby-carriage"></i>
                        Feeding Guide
                    </button>
                    <button class="tab-btn" onclick="showInfantSection('vaccination')">
                        <i class="fas fa-syringe"></i>
                        Vaccination Schedule
                    </button>
                    <button class="tab-btn" onclick="showInfantSection('growth')">
                        <i class="fas fa-chart-line"></i>
                        Growth Tracking
                    </button>
                </div>
            </section>

            <!-- Infant Care Guidance Section -->
            <section class="infant-section active" id="guidance-section">
                <div class="section-header">
                    <h2>Infant Care Guidance</h2>
                    <p>Month-by-month guidance for caring for your baby during the first year</p>
                </div>

                <div class="guidance-timeline">
                    <!-- 0-1 Month -->
                    <div class="guidance-month">
                        <div class="month-header">
                            <div class="month-number">0-1</div>
                            <div class="month-title">
                                <h3>First Month</h3>
                                <p>Newborn Adjustment Period</p>
                            </div>
                        </div>
                        <div class="month-content">
                            <div class="guidance-category">
                                <h4><i class="fas fa-utensils"></i> Feeding</h4>
                                <ul>
                                    <li>Feed every 2-3 hours (8-12 times per day)</li>
                                    <li>Watch for hunger cues: rooting, sucking motions</li>
                                    <li>Burp baby after every feeding</li>
                                    <li>Track wet diapers (6+ per day indicates good feeding)</li>
                                </ul>
                            </div>
                            <div class="guidance-category">
                                <h4><i class="fas fa-moon"></i> Sleep</h4>
                                <ul>
                                    <li>Expect 14-17 hours of sleep per day</li>
                                    <li>Sleep in 2-4 hour stretches</li>
                                    <li>Always place baby on back to sleep</li>
                                    <li>Room-sharing is recommended</li>
                                </ul>
                            </div>
                            <div class="guidance-category">
                                <h4><i class="fas fa-baby"></i> Development</h4>
                                <ul>
                                    <li>Focuses on faces 8-12 inches away</li>
                                    <li>Startles at loud sounds</li>
                                    <li>Begins to lift head briefly during tummy time</li>
                                    <li>Reflexes are strong (grasping, rooting)</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 2-3 Months -->
                    <div class="guidance-month">
                        <div class="month-header">
                            <div class="month-number">2-3</div>
                            <div class="month-title">
                                <h3>Third Month</h3>
                                <p>Social Development</p>
                            </div>
                        </div>
                        <div class="month-content">
                            <div class="guidance-category">
                                <h4><i class="fas fa-utensils"></i> Feeding</h4>
                                <ul>
                                    <li>Feeding becomes more efficient and faster</li>
                                    <li>May go 4-5 hours between feeds</li>
                                    <li>Shows clear hunger and fullness cues</li>
                                    <li>May start showing interest in what others eat</li>
                                </ul>
                            </div>
                            <div class="guidance-category">
                                <h4><i class="fas fa-moon"></i> Sleep</h4>
                                <ul>
                                    <li>May sleep 5-6 hour stretches at night</li>
                                    <li>Develops more regular nap schedule</li>
                                    <li>Total sleep: 13-15 hours per day</li>
                                    <li>Bedtime routine becomes important</li>
                                </ul>
                            </div>
                            <div class="guidance-category">
                                <h4><i class="fas fa-baby"></i> Development</h4>
                                <ul>
                                    <li>Laughs and gurgles</li>
                                    <li>Holds head steady when upright</li>
                                    <li>Follows objects with eyes</li>
                                    <li>Brings hands to mouth</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 3-6 Months -->
                    <div class="guidance-month">
                        <div class="month-header">
                            <div class="month-number">3-6</div>
                            <div class="month-title">
                                <h3>3-6 Months</h3>
                                <p>Active Learning Phase</p>
                            </div>
                        </div>
                        <div class="month-content">
                            <div class="guidance-category">
                                <h4><i class="fas fa-utensils"></i> Feeding</h4>
                                <ul>
                                    <li>Exclusive breastfeeding/formula until 6 months</li>
                                    <li>Feeding every 4-5 hours</li>
                                    <li>Shows interest in food around 5-6 months</li>
                                    <li>May start showing readiness for solids at 6 months</li>
                                </ul>
                            </div>
                            <div class="guidance-category">
                                <h4><i class="fas fa-moon"></i> Sleep</h4>
                                <ul>
                                    <li>May sleep through the night (6-8 hours)</li>
                                    <li>2-3 naps during the day</li>
                                    <li>Total sleep: 12-14 hours per day</li>
                                    <li>Sleep regression may occur around 4 months</li>
                                </ul>
                            </div>
                            <div class="guidance-category">
                                <h4><i class="fas fa-baby"></i> Development</h4>
                                <ul>
                                    <li>Rolls over both ways</li>
                                    <li>Sits with support, may sit alone briefly</li>
                                    <li>Reaches for and grasps objects</li>
                                    <li>Babbles and makes consonant sounds</li>
                                    <li>Recognizes familiar faces</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 6-9 Months -->
                    <div class="guidance-month">
                        <div class="month-header">
                            <div class="month-number">6-9</div>
                            <div class="month-title">
                                <h3>6-9 Months</h3>
                                <p>Mobility & Solids</p>
                            </div>
                        </div>
                        <div class="month-content">
                            <div class="guidance-category">
                                <h4><i class="fas fa-utensils"></i> Feeding</h4>
                                <ul>
                                    <li>Introduction of solid foods (purees, finger foods)</li>
                                    <li>Continue breastfeeding/formula</li>
                                    <li>Offer water in a cup</li>
                                    <li>Watch for food allergies</li>
                                </ul>
                            </div>
                            <div class="guidance-category">
                                <h4><i class="fas fa-moon"></i> Sleep</h4>
                                <ul>
                                    <li>2 naps per day (morning and afternoon)</li>
                                    <li>Sleeps 10-12 hours at night</li>
                                    <li>May experience sleep disruptions due to development</li>
                                    <li>Consistent bedtime routine important</li>
                                </ul>
                            </div>
                            <div class="guidance-category">
                                <h4><i class="fas fa-baby"></i> Development</h4>
                                <ul>
                                    <li>Sits without support</li>
                                    <li>Crawls or scoots</li>
                                    <li>Transfers objects between hands</li>
                                    <li>Says "mama" and "dada" (may not be specific)</li>
                                    <li>Shows stranger anxiety</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 9-12 Months -->
                    <div class="guidance-month">
                        <div class="month-header">
                            <div class="month-number">9-12</div>
                            <div class="month-title">
                                <h3>9-12 Months</h3>
                                <p>Pre-Toddler Stage</p>
                            </div>
                        </div>
                        <div class="month-content">
                            <div class="guidance-category">
                                <h4><i class="fas fa-utensils"></i> Feeding</h4>
                                <ul>
                                    <li>Eats variety of finger foods</li>
                                    <li>Drinks from cup with help</li>
                                    <li>3 meals plus snacks</li>
                                    <li>May start weaning from bottle</li>
                                </ul>
                            </div>
                            <div class="guidance-category">
                                <h4><i class="fas fa-moon"></i> Sleep</h4>
                                <ul>
                                    <li>2 naps transitioning to 1</li>
                                    <li>Sleeps 11-12 hours at night</li>
                                    <li>May resist bedtime more</li>
                                    <li>Sleep training may be needed</li>
                                </ul>
                            </div>
                            <div class="guidance-category">
                                <h4><i class="fas fa-baby"></i> Development</h4>
                                <ul>
                                    <li>Pulls to stand and cruises</li>
                                    <li>May take first steps</li>
                                    <li>Says first words with meaning</li>
                                    <li>Waves bye-bye</li>
                                    <li>Plays peek-a-boo</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Feeding Guide Section -->
            <section class="infant-section" id="feeding-section">
                <div class="section-header">
                    <h2>Feeding Guide</h2>
                    <p>Essential information about breastfeeding and bottle feeding</p>
                </div>

                <div class="feeding-guide">
                    <div class="feeding-tabs">
                        <button class="feeding-tab active" onclick="showFeedingType('breastfeeding')">
                            <i class="fas fa-heart"></i>
                            Breastfeeding
                        </button>
                        <button class="feeding-tab" onclick="showFeedingType('bottle')">
                            <i class="fas fa-baby-carriage"></i>
                            Bottle Feeding
                        </button>
                        <button class="feeding-tab" onclick="showFeedingType('schedule')">
                            <i class="fas fa-clock"></i>
                            Feeding Schedule
                        </button>
                    </div>

                    <!-- Breastfeeding Content -->
                    <div class="feeding-content active" id="breastfeeding-content">
                        <div class="feeding-cards">
                            <div class="feeding-card">
                                <h3>Getting Started</h3>
                                <ul>
                                    <li>Start breastfeeding within the first hour after birth</li>
                                    <li>Feed on demand, typically 8-12 times per day</li>
                                    <li>Look for hunger cues: rooting, sucking motions, fussiness</li>
                                    <li>Ensure proper latch for comfortable feeding</li>
                                    <li>Feed from both breasts during each session</li>
                                </ul>
                            </div>
                            <div class="feeding-card">
                                <h3>Positioning Tips</h3>
                                <ul>
                                    <li>Cradle hold: Baby's head in crook of your arm</li>
                                    <li>Cross-cradle: Support baby's head with opposite hand</li>
                                    <li>Football hold: Baby's body under your arm</li>
                                    <li>Side-lying: Both you and baby lying on your sides</li>
                                    <li>Use pillows for support and comfort</li>
                                </ul>
                            </div>
                            <div class="feeding-card">
                                <h3>Common Challenges</h3>
                                <ul>
                                    <li>Sore nipples: Check latch, use lanolin cream</li>
                                    <li>Engorgement: Feed frequently, use cold compresses</li>
                                    <li>Low milk supply: Feed more often, stay hydrated</li>
                                    <li>Blocked ducts: Massage, warm compresses, frequent feeding</li>
                                    <li>Seek help from lactation consultant if needed</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Bottle Feeding Content -->
                    <div class="feeding-content" id="bottle-content">
                        <div class="feeding-cards">
                            <div class="feeding-card">
                                <h3>Formula Preparation</h3>
                                <ul>
                                    <li>Always wash hands before preparing formula</li>
                                    <li>Use sterile water for mixing (boiled and cooled)</li>
                                    <li>Follow formula instructions exactly</li>
                                    <li>Test temperature on your wrist before feeding</li>
                                    <li>Discard unused formula after 1 hour</li>
                                </ul>
                            </div>
                            <div class="feeding-card">
                                <h3>Feeding Technique</h3>
                                <ul>
                                    <li>Hold baby in semi-upright position</li>
                                    <li>Tilt bottle so nipple is full of milk</li>
                                    <li>Let baby control the pace of feeding</li>
                                    <li>Burp baby halfway through and after feeding</li>
                                    <li>Watch for signs of fullness</li>
                                </ul>
                            </div>
                            <div class="feeding-card">
                                <h3>Amount Guidelines</h3>
                                <ul>
                                    <li>Newborn: 1-3 oz every 2-3 hours</li>
                                    <li>1 month: 2-4 oz every 3-4 hours</li>
                                    <li>2-6 months: 4-6 oz every 3-4 hours</li>
                                    <li>Follow baby's hunger cues</li>
                                    <li>Consult pediatrician for specific guidance</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Feeding Schedule Content -->
                    <div class="feeding-content" id="schedule-content">
                        <div class="schedule-timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker">0-2 weeks</div>
                                <div class="timeline-content">
                                    <h4>Newborn Stage</h4>
                                    <p><strong>Frequency:</strong> Every 1.5-3 hours (8-12 times/day)</p>
                                    <p><strong>Duration:</strong> 15-45 minutes per session</p>
                                    <p><strong>Signs:</strong> Frequent feeding, cluster feeding common</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-marker">2-6 weeks</div>
                                <div class="timeline-content">
                                    <h4>Early Infancy</h4>
                                    <p><strong>Frequency:</strong> Every 2-4 hours (6-8 times/day)</p>
                                    <p><strong>Duration:</strong> 20-40 minutes per session</p>
                                    <p><strong>Signs:</strong> More predictable patterns emerging</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-marker">6-12 weeks</div>
                                <div class="timeline-content">
                                    <h4>Establishing Routine</h4>
                                    <p><strong>Frequency:</strong> Every 3-4 hours (5-6 times/day)</p>
                                    <p><strong>Duration:</strong> 15-30 minutes per session</p>
                                    <p><strong>Signs:</strong> Longer stretches between feeds</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <div class="timeline-marker">3-6 months</div>
                                <div class="timeline-content">
                                    <h4>Settled Pattern</h4>
                                    <p><strong>Frequency:</strong> Every 4-5 hours (4-5 times/day)</p>
                                    <p><strong>Duration:</strong> 15-25 minutes per session</p>
                                    <p><strong>Signs:</strong> Consistent routine, sleeping longer</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Vaccination Schedule Section -->
            <section class="infant-section" id="vaccination-section">
                <div class="section-header">
                    <h2>Vaccination Schedule</h2>
                    <p>Recommended immunization timeline for your baby's first year</p>
                </div>

                <div class="vaccination-timeline">
                    <div class="vaccine-milestone">
                        <div class="milestone-age">Birth</div>
                        <div class="milestone-content">
                            <h4>Hospital Vaccines</h4>
                            <ul class="vaccine-list">
                                <li><strong>Hepatitis B (HepB):</strong> First dose</li>
                                <li><strong>BCG:</strong> If at high risk for tuberculosis</li>
                            </ul>
                            <div class="vaccine-note">
                                <i class="fas fa-info-circle"></i>
                                Given before leaving the hospital
                            </div>
                        </div>
                    </div>

                    <div class="vaccine-milestone">
                        <div class="milestone-age">2 Months</div>
                        <div class="milestone-content">
                            <h4>First Round of Vaccines</h4>
                            <ul class="vaccine-list">
                                <li><strong>DTaP:</strong> Diphtheria, Tetanus, Pertussis</li>
                                <li><strong>IPV:</strong> Inactivated Poliovirus</li>
                                <li><strong>Hib:</strong> Haemophilus influenzae type b</li>
                                <li><strong>PCV13:</strong> Pneumococcal conjugate</li>
                                <li><strong>RV:</strong> Rotavirus</li>
                                <li><strong>HepB:</strong> Second dose</li>
                            </ul>
                        </div>
                    </div>

                    <div class="vaccine-milestone">
                        <div class="milestone-age">4 Months</div>
                        <div class="milestone-content">
                            <h4>Second Round</h4>
                            <ul class="vaccine-list">
                                <li><strong>DTaP:</strong> Second dose</li>
                                <li><strong>IPV:</strong> Second dose</li>
                                <li><strong>Hib:</strong> Second dose</li>
                                <li><strong>PCV13:</strong> Second dose</li>
                                <li><strong>RV:</strong> Second dose</li>
                            </ul>
                        </div>
                    </div>

                    <div class="vaccine-milestone">
                        <div class="milestone-age">6 Months</div>
                        <div class="milestone-content">
                            <h4>Third Round</h4>
                            <ul class="vaccine-list">
                                <li><strong>DTaP:</strong> Third dose</li>
                                <li><strong>IPV:</strong> Third dose</li>
                                <li><strong>Hib:</strong> Third dose</li>
                                <li><strong>PCV13:</strong> Third dose</li>
                                <li><strong>RV:</strong> Third dose</li>
                                <li><strong>HepB:</strong> Third dose</li>
                                <li><strong>Influenza:</strong> Annual vaccine (6+ months)</li>
                            </ul>
                        </div>
                    </div>

                    <div class="vaccine-milestone">
                        <div class="milestone-age">12-15 Months</div>
                        <div class="milestone-content">
                            <h4>Toddler Vaccines</h4>
                            <ul class="vaccine-list">
                                <li><strong>MMR:</strong> Measles, Mumps, Rubella</li>
                                <li><strong>Varicella:</strong> Chickenpox</li>
                                <li><strong>PCV13:</strong> Fourth dose</li>
                                <li><strong>Hib:</strong> Fourth dose</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="vaccine-reminders">
                    <div class="reminder-card">
                        <i class="fas fa-calendar-check"></i>
                        <h4>Important Reminders</h4>
                        <ul>
                            <li>Keep vaccination records in a safe place</li>
                            <li>Schedule appointments in advance</li>
                            <li>Bring comfort items for your baby</li>
                            <li>Ask about pain relief options</li>
                            <li>Monitor for side effects after vaccination</li>
                        </ul>
                    </div>
                    <div class="reminder-card">
                        <i class="fas fa-thermometer-half"></i>
                        <h4>After Vaccination Care</h4>
                        <ul>
                            <li>Watch for fever, fussiness, or swelling</li>
                            <li>Give extra cuddles and comfort</li>
                            <li>Use cool compress for injection site</li>
                            <li>Contact doctor if high fever persists</li>
                            <li>Keep baby hydrated</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Growth Tracking Section -->
            <section class="infant-section" id="growth-section">
                <div class="section-header">
                    <h2>Growth Tracking</h2>
                    <p>Monitor your baby's development milestones and growth patterns</p>
                </div>

                <div class="growth-categories">
                    <!-- Physical Growth -->
                    <div class="growth-card">
                        <div class="growth-header">
                            <i class="fas fa-ruler"></i>
                            <h3>Physical Growth Milestones</h3>
                        </div>
                        <div class="growth-content">
                            <div class="milestone-timeline">
                                <div class="milestone-item">
                                    <div class="milestone-age-badge">0-3 Months</div>
                                    <div class="milestone-details">
                                        <h5>Weight & Length</h5>
                                        <ul>
                                            <li>Birth weight doubles by 4-6 months</li>
                                            <li>Gains 5-7 oz per week initially</li>
                                            <li>Grows 1-1.5 inches per month</li>
                                            <li>Head circumference increases rapidly</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="milestone-item">
                                    <div class="milestone-age-badge">3-6 Months</div>
                                    <div class="milestone-details">
                                        <h5>Steady Growth</h5>
                                        <ul>
                                            <li>Weight gain slows to 3-5 oz per week</li>
                                            <li>Length increases 0.5-1 inch per month</li>
                                            <li>Can sit with support</li>
                                            <li>Better head control</li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="milestone-item">
                                    <div class="milestone-age-badge">6-12 Months</div>
                                    <div class="milestone-details">
                                        <h5>Active Growth</h5>
                                        <ul>
                                            <li>Birth weight triples by 12 months</li>
                                            <li>Length increases by 50% from birth</li>
                                            <li>Crawling and cruising</li>
                                            <li>First teeth appear</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Developmental Milestones -->
                    <div class="growth-card">
                        <div class="growth-header">
                            <i class="fas fa-brain"></i>
                            <h3>Developmental Milestones</h3>
                        </div>
                        <div class="growth-content">
                            <div class="development-tabs">
                                <button class="dev-tab active" onclick="showDevelopment('motor')">Motor Skills</button>
                                <button class="dev-tab" onclick="showDevelopment('cognitive')">Cognitive</button>
                                <button class="dev-tab" onclick="showDevelopment('social')">Social</button>
                            </div>

                            <div class="development-content active" id="motor-development">
                                <div class="dev-milestone">
                                    <strong>0-3 Months:</strong>
                                    <ul>
                                        <li>Lifts head when on tummy</li>
                                        <li>Follows objects with eyes</li>
                                        <li>Grasps reflexively</li>
                                        <li>Brings hands to mouth</li>
                                    </ul>
                                </div>
                                <div class="dev-milestone">
                                    <strong>3-6 Months:</strong>
                                    <ul>
                                        <li>Rolls over both ways</li>
                                        <li>Sits with support</li>
                                        <li>Reaches for toys</li>
                                        <li>Transfers objects hand to hand</li>
                                    </ul>
                                </div>
                                <div class="dev-milestone">
                                    <strong>6-12 Months:</strong>
                                    <ul>
                                        <li>Sits without support</li>
                                        <li>Crawls or scoots</li>
                                        <li>Pulls to standing</li>
                                        <li>Pincer grasp develops</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="development-content" id="cognitive-development">
                                <div class="dev-milestone">
                                    <strong>0-3 Months:</strong>
                                    <ul>
                                        <li>Recognizes familiar faces</li>
                                        <li>Responds to sounds</li>
                                        <li>Begins to smile socially</li>
                                        <li>Shows interest in faces</li>
                                    </ul>
                                </div>
                                <div class="dev-milestone">
                                    <strong>3-6 Months:</strong>
                                    <ul>
                                        <li>Laughs and squeals</li>
                                        <li>Explores with mouth</li>
                                        <li>Shows curiosity about environment</li>
                                        <li>Begins to understand cause and effect</li>
                                    </ul>
                                </div>
                                <div class="dev-milestone">
                                    <strong>6-12 Months:</strong>
                                    <ul>
                                        <li>Understands simple words</li>
                                        <li>Responds to name</li>
                                        <li>Imitates sounds and gestures</li>
                                        <li>Shows object permanence</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="development-content" id="social-development">
                                <div class="dev-milestone">
                                    <strong>0-3 Months:</strong>
                                    <ul>
                                        <li>Makes eye contact</li>
                                        <li>Smiles responsively</li>
                                        <li>Calms when comforted</li>
                                        <li>Shows preference for familiar people</li>
                                    </ul>
                                </div>
                                <div class="dev-milestone">
                                    <strong>3-6 Months:</strong>
                                    <ul>
                                        <li>Enjoys social play</li>
                                        <li>Shows excitement when seeing familiar people</li>
                                        <li>Begins to show stranger awareness</li>
                                        <li>Expresses emotions clearly</li>
                                    </ul>
                                </div>
                                <div class="dev-milestone">
                                    <strong>6-12 Months:</strong>
                                    <ul>
                                        <li>Shows separation anxiety</li>
                                        <li>Plays peek-a-boo</li>
                                        <li>Waves bye-bye</li>
                                        <li>Shows affection to familiar people</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Growth Tracking Tools -->
                    <div class="growth-card">
                        <div class="growth-header">
                            <i class="fas fa-chart-line"></i>
                            <h3>Track Your Baby's Growth</h3>
                        </div>
                        <div class="growth-content">
                            <div class="tracking-form">
                                <h4>Record Measurements</h4>
                                <form id="growthForm">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="measurementDate">Date:</label>
                                            <input type="date" id="measurementDate" class="form-input" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="babyAge">Age (weeks):</label>
                                            <input type="number" id="babyAge" class="form-input" placeholder="8" required>
                                        </div>
                                    </div>
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="weight">Weight (kg):</label>
                                            <input type="number" id="weight" class="form-input" step="0.1" placeholder="4.5" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="length">Length (cm):</label>
                                            <input type="number" id="length" class="form-input" step="0.1" placeholder="55" required>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="headCircumference">Head Circumference (cm):</label>
                                        <input type="number" id="headCircumference" class="form-input" step="0.1" placeholder="38">
                                    </div>
                                    <button type="submit" class="btn btn-primary">Record Measurement</button>
                                </form>
                            </div>

                            <div class="growth-records" id="growthRecords">
                                <h4>Growth Records</h4>
                                <div class="records-list" id="recordsList">
                                    <p class="no-records">No measurements recorded yet. Add your first measurement above!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-heart"></i>
                        <span>MomCare</span>
                    </div>
                    <p>Empowering mothers with knowledge and support throughout their journey.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="pregnancy-care.html">Pregnancy Care</a></li>
                        <li><a href="infant-care.html">Infant Care</a></li>
                        <li><a href="tools.html">Tools</a></li>
                        <li><a href="forum.html">Forum</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 MomCare. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize infant care page
        document.addEventListener('DOMContentLoaded', function() {
            const user = getCurrentUser();
            if (user) {
                document.getElementById('userGreeting').textContent = `Hi, ${user.name || user.fullName || 'User'}!`;
            }

            // Load checklist state from localStorage
            loadChecklistState();
        });

        // Section navigation
        function showInfantSection(section) {
            // Hide all sections
            document.querySelectorAll('.infant-section').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected section
            document.getElementById(section + '-section').classList.add('active');

            // Add active class to clicked tab
            event.target.closest('.tab-btn').classList.add('active');
        }

        // Feeding type navigation
        function showFeedingType(type) {
            // Hide all feeding content
            document.querySelectorAll('.feeding-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all feeding tabs
            document.querySelectorAll('.feeding-tab').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected feeding content
            document.getElementById(type + '-content').classList.add('active');

            // Add active class to clicked tab
            event.target.closest('.feeding-tab').classList.add('active');
        }

        // Checklist functionality
        function loadChecklistState() {
            const checklistState = JSON.parse(localStorage.getItem('infantChecklistState') || '{}');

            document.querySelectorAll('.checklist-item input[type="checkbox"]').forEach((checkbox, index) => {
                const itemText = checkbox.parentElement.textContent.trim();
                if (checklistState[itemText]) {
                    checkbox.checked = true;
                }

                checkbox.addEventListener('change', function() {
                    saveChecklistState();
                });
            });
        }

        function saveChecklistState() {
            const checklistState = {};

            document.querySelectorAll('.checklist-item input[type="checkbox"]').forEach(checkbox => {
                const itemText = checkbox.parentElement.textContent.trim();
                checklistState[itemText] = checkbox.checked;
            });

            localStorage.setItem('infantChecklistState', JSON.stringify(checklistState));
        }

        // Development milestone navigation
        function showDevelopment(type) {
            // Hide all development content
            document.querySelectorAll('.development-content').forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all dev tabs
            document.querySelectorAll('.dev-tab').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected development content
            document.getElementById(type + '-development').classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Growth tracking functionality
        document.addEventListener('DOMContentLoaded', function() {
            const growthForm = document.getElementById('growthForm');
            if (growthForm) {
                growthForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    recordGrowthMeasurement();
                });
            }

            // Load existing growth records
            loadGrowthRecords();
        });

        function recordGrowthMeasurement() {
            const formData = {
                date: document.getElementById('measurementDate').value,
                age: document.getElementById('babyAge').value,
                weight: document.getElementById('weight').value,
                length: document.getElementById('length').value,
                headCircumference: document.getElementById('headCircumference').value || 'Not recorded'
            };

            // Validate required fields
            if (!formData.date || !formData.age || !formData.weight || !formData.length) {
                alert('Please fill in all required fields.');
                return;
            }

            // Get existing records
            const existingRecords = JSON.parse(localStorage.getItem('growthRecords') || '[]');

            // Add new record
            existingRecords.push({
                ...formData,
                id: Date.now(),
                timestamp: new Date().toISOString()
            });

            // Sort by age
            existingRecords.sort((a, b) => parseInt(a.age) - parseInt(b.age));

            // Save to localStorage
            localStorage.setItem('growthRecords', JSON.stringify(existingRecords));

            // Clear form
            document.getElementById('growthForm').reset();

            // Reload records display
            loadGrowthRecords();

            // Show success message
            alert('Growth measurement recorded successfully!');
        }

        function loadGrowthRecords() {
            const records = JSON.parse(localStorage.getItem('growthRecords') || '[]');
            const recordsList = document.getElementById('recordsList');

            if (records.length === 0) {
                recordsList.innerHTML = '<p class="no-records">No measurements recorded yet. Add your first measurement above!</p>';
                return;
            }

            let recordsHTML = '';
            records.forEach(record => {
                recordsHTML += `
                    <div class="growth-record">
                        <div class="record-header">
                            <strong>Age: ${record.age} weeks</strong>
                            <span class="record-date">${formatDate(record.date)}</span>
                        </div>
                        <div class="record-details">
                            <span>Weight: ${record.weight} kg</span>
                            <span>Length: ${record.length} cm</span>
                            <span>Head: ${record.headCircumference} cm</span>
                        </div>
                        <button class="delete-record" onclick="deleteGrowthRecord(${record.id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
            });

            recordsList.innerHTML = recordsHTML;
        }

        function deleteGrowthRecord(recordId) {
            if (confirm('Are you sure you want to delete this record?')) {
                const records = JSON.parse(localStorage.getItem('growthRecords') || '[]');
                const updatedRecords = records.filter(record => record.id !== recordId);
                localStorage.setItem('growthRecords', JSON.stringify(updatedRecords));
                loadGrowthRecords();
            }
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }
    </script>
</body>
</html>
