from django import forms
from django.contrib.auth.forms import UserCreationForm
from .models import CustomUser


class CustomUserCreationForm(UserCreationForm):
    """Custom user registration form"""
    email = forms.EmailField(required=True)
    full_name = forms.CharField(max_length=255, required=False)
    due_date = forms.DateField(required=False, widget=forms.DateInput(attrs={'type': 'date'}))
    
    class Meta:
        model = CustomUser
        fields = ('username', 'email', 'full_name', 'due_date', 'password1', 'password2')
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['username'].widget.attrs.update({'class': 'form-input', 'placeholder': 'Username'})
        self.fields['email'].widget.attrs.update({'class': 'form-input', 'placeholder': 'Email'})
        self.fields['full_name'].widget.attrs.update({'class': 'form-input', 'placeholder': 'Full Name'})
        self.fields['due_date'].widget.attrs.update({'class': 'form-input'})
        self.fields['password1'].widget.attrs.update({'class': 'form-input', 'placeholder': 'Password'})
        self.fields['password2'].widget.attrs.update({'class': 'form-input', 'placeholder': 'Confirm Password'})
    
    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.full_name = self.cleaned_data['full_name']
        user.due_date = self.cleaned_data['due_date']
        if user.due_date:
            user.is_pregnant = True
        if commit:
            user.save()
        return user


class UserProfileForm(forms.ModelForm):
    """User profile update form"""
    
    class Meta:
        model = CustomUser
        fields = [
            'full_name', 'email', 'phone_number', 'date_of_birth',
            'emergency_contact', 'emergency_phone', 'profile_picture',
            'is_pregnant', 'due_date', 'pre_pregnancy_weight', 'current_weight',
            'height', 'email_notifications', 'sms_notifications'
        ]
        widgets = {
            'full_name': forms.TextInput(attrs={'class': 'form-input'}),
            'email': forms.EmailInput(attrs={'class': 'form-input'}),
            'phone_number': forms.TextInput(attrs={'class': 'form-input'}),
            'date_of_birth': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
            'emergency_contact': forms.TextInput(attrs={'class': 'form-input'}),
            'emergency_phone': forms.TextInput(attrs={'class': 'form-input'}),
            'due_date': forms.DateInput(attrs={'class': 'form-input', 'type': 'date'}),
            'pre_pregnancy_weight': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.1'}),
            'current_weight': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.1'}),
            'height': forms.NumberInput(attrs={'class': 'form-input', 'step': '0.1'}),
            'email_notifications': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
            'sms_notifications': forms.CheckboxInput(attrs={'class': 'form-checkbox'}),
        }
