{% extends 'base.html' %}
{% load static %}

{% block title %}Register - MATERNIFY{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/auth.css' %}">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="auth-container">
    <a href="{% url 'core:index' %}" class="back-to-home">
        <i class="fas fa-arrow-left"></i>
        Back to Home
    </a>

    <div class="auth-card">
        <div class="auth-logo">
            <img src="{% static 'images/logo.svg' %}" alt="MATERNIFY Logo">
            <span>MATERNIFY</span>
        </div>

        <h1 class="auth-title">Join MATERNIFY</h1>
        <p class="auth-subtitle">Create your account to get started</p>

        {% if messages %}
            <div id="alert-container">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}">
                        <span>{{ message }}</span>
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <form class="auth-form" method="post">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="{{ form.username.id_for_label }}" class="form-label">Username</label>
                {{ form.username }}
                {% if form.username.errors %}
                    <div class="form-error show">{{ form.username.errors.0 }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                {{ form.email }}
                {% if form.email.errors %}
                    <div class="form-error show">{{ form.email.errors.0 }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.full_name.id_for_label }}" class="form-label">Full Name</label>
                {{ form.full_name }}
                {% if form.full_name.errors %}
                    <div class="form-error show">{{ form.full_name.errors.0 }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.password1.id_for_label }}" class="form-label">Password</label>
                <div class="password-container">
                    {{ form.password1 }}
                    <button type="button" class="password-toggle" onclick="togglePassword('{{ form.password1.id_for_label }}')">
                        <i class="fas fa-eye" id="{{ form.password1.id_for_label }}ToggleIcon"></i>
                    </button>
                </div>
                {% if form.password1.errors %}
                    <div class="form-error show">{{ form.password1.errors.0 }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.password2.id_for_label }}" class="form-label">Confirm Password</label>
                <div class="password-container">
                    {{ form.password2 }}
                    <button type="button" class="password-toggle" onclick="togglePassword('{{ form.password2.id_for_label }}')">
                        <i class="fas fa-eye" id="{{ form.password2.id_for_label }}ToggleIcon"></i>
                    </button>
                </div>
                {% if form.password2.errors %}
                    <div class="form-error show">{{ form.password2.errors.0 }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.due_date.id_for_label }}" class="form-label">Expected Due Date (Optional)</label>
                {{ form.due_date }}
                {% if form.due_date.errors %}
                    <div class="form-error show">{{ form.due_date.errors.0 }}</div>
                {% endif %}
            </div>

            <button type="submit" class="auth-button">
                Create Account
            </button>
        </form>

        <div class="auth-divider">
            <span>or</span>
        </div>

        <div class="auth-link">
            <p>Already have an account? <a href="{% url 'accounts:login' %}">Sign in here</a></p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/main.js' %}"></script>
<script src="{% static 'js/auth.js' %}"></script>
<script>
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const icon = document.getElementById(fieldId + 'ToggleIcon');

        if (field.type === 'password') {
            field.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            field.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }
</script>
{% endblock %}
