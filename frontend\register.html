<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - MATERNIFY</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="auth-container">
        <a href="index.html" class="back-to-home">
            <i class="fas fa-arrow-left"></i>
            Back to Home
        </a>

        <div class="auth-card">
            <div class="auth-logo">
                <img src="images/logo.svg" alt="MATERNIFY Logo">
                <span>MATERNIFY</span>
            </div>

            <h1 class="auth-title">Join MATERNIFY</h1>
            <p class="auth-subtitle">Create your account to get started</p>

            <div id="alert-container"></div>

            <form class="auth-form" id="registerForm">
                <div class="form-group">
                    <label for="fullName" class="form-label">Full Name</label>
                    <input
                        type="text"
                        id="fullName"
                        name="fullName"
                        class="form-input"
                        placeholder="Enter your full name"
                        required
                    >
                    <div class="form-error" id="fullNameError"></div>
                </div>

                <div class="form-group">
                    <label for="email" class="form-label">Email Address</label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="form-input"
                        placeholder="Enter your email"
                        required
                    >
                    <div class="form-error" id="emailError"></div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <div class="password-container">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="form-input"
                            placeholder="Create a password"
                            required
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                    <div class="password-strength">
                        <div class="strength-bar">
                            <div class="strength-fill" id="strengthFill"></div>
                        </div>
                        <div class="strength-text" id="strengthText">Password strength</div>
                    </div>
                    <div class="form-error" id="passwordError"></div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword" class="form-label">Confirm Password</label>
                    <div class="password-container">
                        <input
                            type="password"
                            id="confirmPassword"
                            name="confirmPassword"
                            class="form-input"
                            placeholder="Confirm your password"
                            required
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                            <i class="fas fa-eye" id="confirmPasswordToggleIcon"></i>
                        </button>
                    </div>
                    <div class="form-error" id="confirmPasswordError"></div>
                </div>

                <div class="form-group">
                    <label for="dueDate" class="form-label">Expected Due Date (Optional)</label>
                    <input
                        type="date"
                        id="dueDate"
                        name="dueDate"
                        class="form-input"
                    >
                    <div class="form-error" id="dueDateError"></div>
                </div>

                <button type="submit" class="auth-button" id="registerButton">
                    Create Account
                </button>
            </form>

            <div class="auth-divider">
                <span>or</span>
            </div>

            <div class="auth-link">
                <p>Already have an account? <a href="login.html">Sign in here</a></p>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script>
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = document.getElementById(fieldId + 'ToggleIcon');

            if (field.type === 'password') {
                field.type = 'text';
                icon.className = 'fas fa-eye-slash';
            } else {
                field.type = 'password';
                icon.className = 'fas fa-eye';
            }
        }

        // Password strength checker
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');

            const strength = calculatePasswordStrength(password);

            strengthFill.className = 'strength-fill';

            if (password.length === 0) {
                strengthText.textContent = 'Password strength';
                return;
            }

            if (strength < 3) {
                strengthFill.classList.add('weak');
                strengthText.textContent = 'Weak password';
            } else if (strength < 5) {
                strengthFill.classList.add('medium');
                strengthText.textContent = 'Medium password';
            } else {
                strengthFill.classList.add('strong');
                strengthText.textContent = 'Strong password';
            }
        });

        function calculatePasswordStrength(password) {
            let strength = 0;

            if (password.length >= 8) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;

            return strength;
        }

        // Registration form handling
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                fullName: document.getElementById('fullName').value,
                email: document.getElementById('email').value,
                password: document.getElementById('password').value,
                confirmPassword: document.getElementById('confirmPassword').value,
                dueDate: document.getElementById('dueDate').value
            };

            // Clear previous errors
            clearErrors();

            // Validate form
            if (!validateRegisterForm(formData)) {
                return;
            }

            // Show loading state
            const button = document.getElementById('registerButton');
            const hideLoading = showLoading(button);

            // Simulate API call
            setTimeout(() => {
                hideLoading();

                // Store user session
                localStorage.setItem('userLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify({
                    name: formData.fullName,
                    email: formData.email,
                    dueDate: formData.dueDate,
                    registrationTime: new Date().toISOString()
                }));

                showAlert('Account created successfully! Redirecting...', 'success');

                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            }, 2000);
        });

        function validateRegisterForm(formData) {
            let isValid = true;

            if (!formData.fullName.trim()) {
                showFieldError('fullNameError', 'Full name is required');
                isValid = false;
            } else if (formData.fullName.trim().length < 2) {
                showFieldError('fullNameError', 'Full name must be at least 2 characters');
                isValid = false;
            }

            if (!formData.email) {
                showFieldError('emailError', 'Email is required');
                isValid = false;
            } else if (!validateEmail(formData.email)) {
                showFieldError('emailError', 'Please enter a valid email address');
                isValid = false;
            }

            if (!formData.password) {
                showFieldError('passwordError', 'Password is required');
                isValid = false;
            } else if (formData.password.length < 6) {
                showFieldError('passwordError', 'Password must be at least 6 characters');
                isValid = false;
            }

            if (!formData.confirmPassword) {
                showFieldError('confirmPasswordError', 'Please confirm your password');
                isValid = false;
            } else if (formData.password !== formData.confirmPassword) {
                showFieldError('confirmPasswordError', 'Passwords do not match');
                isValid = false;
            }

            if (formData.dueDate) {
                const dueDate = new Date(formData.dueDate);
                const today = new Date();
                const maxDate = new Date();
                maxDate.setMonth(maxDate.getMonth() + 10); // 10 months from now

                if (dueDate < today) {
                    showFieldError('dueDateError', 'Due date cannot be in the past');
                    isValid = false;
                } else if (dueDate > maxDate) {
                    showFieldError('dueDateError', 'Due date seems too far in the future');
                    isValid = false;
                }
            }

            return isValid;
        }

        function showFieldError(fieldId, message) {
            const errorElement = document.getElementById(fieldId);
            errorElement.textContent = message;
            errorElement.classList.add('show');

            let inputField = errorElement.previousElementSibling;
            if (inputField.classList.contains('password-strength')) {
                inputField = inputField.previousElementSibling;
            }

            if (inputField.classList.contains('password-container')) {
                inputField.querySelector('input').classList.add('error');
            } else {
                inputField.classList.add('error');
            }
        }

        function clearErrors() {
            document.querySelectorAll('.form-error').forEach(error => {
                error.classList.remove('show');
                error.textContent = '';
            });

            document.querySelectorAll('.form-input').forEach(input => {
                input.classList.remove('error', 'success');
            });
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            alertContainer.innerHTML = `
                <div class="alert alert-${type}">
                    <span>${message}</span>
                    <button class="alert-close" onclick="this.parentElement.remove()">&times;</button>
                </div>
            `;
        }
    </script>
</body>
</html>
