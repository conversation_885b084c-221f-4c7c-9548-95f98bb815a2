from django.contrib import admin
from .models import Article, ForumPost, ForumReply, Resource, Appointment


@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    """Admin configuration for Article"""
    list_display = ('title', 'category', 'author', 'is_published', 'created_at')
    list_filter = ('category', 'is_published', 'created_at')
    search_fields = ('title', 'content', 'author__username')
    prepopulated_fields = {'slug': ('title',)}
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'


@admin.register(ForumPost)
class ForumPostAdmin(admin.ModelAdmin):
    """Admin configuration for ForumPost"""
    list_display = ('title', 'author', 'is_pinned', 'views', 'created_at')
    list_filter = ('is_pinned', 'created_at')
    search_fields = ('title', 'content', 'author__username')
    ordering = ('-is_pinned', '-created_at')
    date_hierarchy = 'created_at'


@admin.register(ForumReply)
class ForumReplyAdmin(admin.ModelAdmin):
    """Admin configuration for ForumReply"""
    list_display = ('post', 'author', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('content', 'author__username', 'post__title')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'


@admin.register(Resource)
class ResourceAdmin(admin.ModelAdmin):
    """Admin configuration for Resource"""
    list_display = ('title', 'resource_type', 'category', 'is_featured', 'created_at')
    list_filter = ('resource_type', 'category', 'is_featured', 'created_at')
    search_fields = ('title', 'description')
    ordering = ('-is_featured', '-created_at')
    date_hierarchy = 'created_at'


@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    """Admin configuration for Appointment"""
    list_display = ('title', 'user', 'appointment_date', 'doctor_name', 'is_completed')
    list_filter = ('is_completed', 'appointment_date')
    search_fields = ('title', 'user__username', 'doctor_name')
    ordering = ('appointment_date',)
    date_hierarchy = 'appointment_date'
