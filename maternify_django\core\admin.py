from django.contrib import admin
from .models import Article, ForumPost, ForumReply, Resource, Appointment, TeamMember, Testimonial, CompanyMilestone, ContactInquiry


@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    """Admin configuration for Article"""
    list_display = ('title', 'category', 'author', 'is_published', 'created_at')
    list_filter = ('category', 'is_published', 'created_at')
    search_fields = ('title', 'content', 'author__username')
    prepopulated_fields = {'slug': ('title',)}
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'


@admin.register(ForumPost)
class ForumPostAdmin(admin.ModelAdmin):
    """Admin configuration for ForumPost"""
    list_display = ('title', 'author', 'is_pinned', 'views', 'created_at')
    list_filter = ('is_pinned', 'created_at')
    search_fields = ('title', 'content', 'author__username')
    ordering = ('-is_pinned', '-created_at')
    date_hierarchy = 'created_at'


@admin.register(ForumReply)
class ForumReplyAdmin(admin.ModelAdmin):
    """Admin configuration for ForumReply"""
    list_display = ('post', 'author', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('content', 'author__username', 'post__title')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'


@admin.register(Resource)
class ResourceAdmin(admin.ModelAdmin):
    """Admin configuration for Resource"""
    list_display = ('title', 'resource_type', 'category', 'is_featured', 'created_at')
    list_filter = ('resource_type', 'category', 'is_featured', 'created_at')
    search_fields = ('title', 'description')
    ordering = ('-is_featured', '-created_at')
    date_hierarchy = 'created_at'


@admin.register(Appointment)
class AppointmentAdmin(admin.ModelAdmin):
    """Admin configuration for Appointment"""
    list_display = ('title', 'user', 'appointment_date', 'doctor_name', 'is_completed')
    list_filter = ('is_completed', 'appointment_date')
    search_fields = ('title', 'user__username', 'doctor_name')
    ordering = ('appointment_date',)
    date_hierarchy = 'appointment_date'


@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    """Admin configuration for TeamMember"""
    list_display = ('name', 'role', 'specialization', 'years_experience', 'is_active', 'order')
    list_filter = ('is_active', 'role')
    search_fields = ('name', 'role', 'specialization')
    list_editable = ('order', 'is_active')
    ordering = ('order', 'name')


@admin.register(Testimonial)
class TestimonialAdmin(admin.ModelAdmin):
    """Admin configuration for Testimonial"""
    list_display = ('user', 'rating', 'is_featured', 'is_approved', 'created_at')
    list_filter = ('rating', 'is_featured', 'is_approved', 'created_at')
    search_fields = ('user__username', 'user__email', 'content')
    list_editable = ('is_featured', 'is_approved')
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'


@admin.register(CompanyMilestone)
class CompanyMilestoneAdmin(admin.ModelAdmin):
    """Admin configuration for CompanyMilestone"""
    list_display = ('year', 'title', 'is_active', 'order')
    list_filter = ('year', 'is_active')
    search_fields = ('title', 'description')
    list_editable = ('order', 'is_active')
    ordering = ('order', 'year')


@admin.register(ContactInquiry)
class ContactInquiryAdmin(admin.ModelAdmin):
    """Admin configuration for ContactInquiry"""
    list_display = ('name', 'email', 'subject', 'inquiry_type', 'is_resolved', 'created_at')
    list_filter = ('inquiry_type', 'is_resolved', 'created_at')
    search_fields = ('name', 'email', 'subject', 'message')
    list_editable = ('is_resolved',)
    readonly_fields = ('created_at',)
    date_hierarchy = 'created_at'

    def mark_resolved(self, request, queryset):
        from django.utils import timezone
        queryset.update(is_resolved=True, resolved_at=timezone.now())
    mark_resolved.short_description = "Mark selected inquiries as resolved"

    actions = ['mark_resolved']
