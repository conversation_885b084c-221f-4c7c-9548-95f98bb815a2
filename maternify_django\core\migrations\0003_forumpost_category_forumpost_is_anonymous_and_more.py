# Generated by Django 5.2.3 on 2025-06-13 05:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_companymilestone_contactinquiry_teammember_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='forumpost',
            name='category',
            field=models.CharField(choices=[('pregnancy', 'Pregnancy Journey'), ('newborn', 'Newborn Care'), ('feeding', 'Feeding & Nutrition'), ('health', 'Health & Wellness'), ('support', 'Emotional Support'), ('general', 'General Discussion')], default='general', max_length=20),
        ),
        migrations.AddField(
            model_name='forumpost',
            name='is_anonymous',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='forumpost',
            name='likes',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='forumreply',
            name='is_anonymous',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='forumreply',
            name='likes',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='forumpost',
            name='author',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='forum_posts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='forumreply',
            name='author',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='forum_replies', to=settings.AUTH_USER_MODEL),
        ),
    ]
