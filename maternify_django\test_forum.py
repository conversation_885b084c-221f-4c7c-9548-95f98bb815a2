#!/usr/bin/env python
"""
Test script to verify forum functionality
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'maternify.settings')
django.setup()

from core.models import ForumPost, ForumReply
from django.contrib.auth import get_user_model

User = get_user_model()

def test_forum_functionality():
    print("🧪 Testing Forum Functionality...")
    print("=" * 50)
    
    # Test 1: Check if models are working
    print("1. Testing ForumPost model...")
    posts = ForumPost.objects.all()
    print(f"   ✅ Found {posts.count()} forum posts")
    
    if posts.exists():
        first_post = posts.first()
        print(f"   📝 Sample post: '{first_post.title}'")
        print(f"   👤 Author: {first_post.get_author_display()}")
        print(f"   📂 Category: {first_post.get_category_display()}")
        print(f"   👀 Views: {first_post.views}")
        print(f"   ❤️ Likes: {first_post.likes}")
        print(f"   💬 Replies: {first_post.reply_count}")
    
    print()
    
    # Test 2: Check replies
    print("2. Testing ForumReply model...")
    replies = ForumReply.objects.all()
    print(f"   ✅ Found {replies.count()} forum replies")
    
    if replies.exists():
        first_reply = replies.first()
        print(f"   💬 Sample reply: '{first_reply.content[:50]}...'")
        print(f"   👤 Author: {first_reply.get_author_display()}")
        print(f"   📝 Post: '{first_reply.post.title}'")
    
    print()
    
    # Test 3: Check categories
    print("3. Testing category distribution...")
    categories = ForumPost.CATEGORY_CHOICES
    for code, name in categories:
        count = ForumPost.objects.filter(category=code).count()
        print(f"   📂 {name}: {count} posts")
    
    print()
    
    # Test 4: Check users
    print("4. Testing user data...")
    users = User.objects.all()
    print(f"   ✅ Found {users.count()} users")
    
    for user in users:
        post_count = user.forum_posts.count()
        reply_count = user.forum_replies.count()
        print(f"   👤 {user.username}: {post_count} posts, {reply_count} replies")
    
    print()
    
    # Test 5: Test model methods
    print("5. Testing model methods...")
    if posts.exists():
        test_post = posts.first()
        
        # Test view increment
        original_views = test_post.views
        test_post.increment_views()
        test_post.refresh_from_db()
        print(f"   👀 View increment: {original_views} → {test_post.views}")
        
        # Test URL generation
        print(f"   🔗 Post URL: {test_post.get_absolute_url()}")
    
    print()
    print("🎉 Forum functionality test completed!")
    print("=" * 50)

if __name__ == "__main__":
    test_forum_functionality()
