// Authentication JavaScript functionality

// Initialize authentication on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeAuth();
});

function initializeAuth() {
    // Check if user is already logged in and redirect if necessary
    const currentPage = window.location.pathname.split('/').pop();

    if (isLoggedIn() && (currentPage === 'login.html' || currentPage === 'register.html')) {
        window.location.href = 'dashboard.html';
        return;
    }

    // Update navigation if user is logged in
    updateNavigation();
}

// User registration function
function registerUser(userData) {
    return new Promise((resolve, reject) => {
        // Simulate API call delay
        setTimeout(() => {
            try {
                // Validate user data
                if (!userData.fullName || !userData.email || !userData.password) {
                    throw new Error('Missing required fields');
                }

                if (!validateEmail(userData.email)) {
                    throw new Error('Invalid email format');
                }

                if (userData.password.length < 6) {
                    throw new Error('Password must be at least 6 characters');
                }

                // Check if user already exists (simulate)
                const existingUsers = getStoredUsers();
                if (existingUsers.find(user => user.email === userData.email)) {
                    throw new Error('User with this email already exists');
                }

                // Create new user
                const newUser = {
                    id: generateUserId(),
                    fullName: userData.fullName,
                    email: userData.email,
                    password: hashPassword(userData.password), // In real app, this would be hashed server-side
                    dueDate: userData.dueDate || null,
                    registrationDate: new Date().toISOString(),
                    profile: {
                        avatar: null,
                        preferences: {
                            notifications: true,
                            emailUpdates: true
                        }
                    }
                };

                // Store user
                existingUsers.push(newUser);
                localStorage.setItem('registeredUsers', JSON.stringify(existingUsers));

                // Log user in
                loginUser(newUser);

                resolve({
                    success: true,
                    message: 'Registration successful',
                    user: sanitizeUser(newUser)
                });

            } catch (error) {
                reject({
                    success: false,
                    message: error.message
                });
            }
        }, 1500);
    });
}

// User login function
function loginUser(userData) {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            try {
                let user;

                // Check for demo credentials
                if (userData.email === '<EMAIL>' && userData.password === 'demo123') {
                    user = {
                        id: 'demo',
                        fullName: 'Demo User',
                        email: '<EMAIL>',
                        dueDate: '2024-08-15',
                        registrationDate: new Date().toISOString(),
                        profile: {
                            avatar: null,
                            preferences: {
                                notifications: true,
                                emailUpdates: true
                            }
                        }
                    };
                } else {
                    // Check registered users
                    const existingUsers = getStoredUsers();
                    user = existingUsers.find(u =>
                        u.email === userData.email &&
                        u.password === hashPassword(userData.password)
                    );

                    if (!user) {
                        throw new Error('Invalid email or password');
                    }
                }

                // Set user session
                localStorage.setItem('userLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify(sanitizeUser(user)));
                localStorage.setItem('loginTime', new Date().toISOString());

                resolve({
                    success: true,
                    message: 'Login successful',
                    user: sanitizeUser(user)
                });

            } catch (error) {
                reject({
                    success: false,
                    message: error.message
                });
            }
        }, 1000);
    });
}

// Logout function
function logoutUser() {
    localStorage.removeItem('userLoggedIn');
    localStorage.removeItem('currentUser');
    localStorage.removeItem('loginTime');

    // Clear any cached data
    localStorage.removeItem('pregnancyData');
    localStorage.removeItem('infantData');

    window.location.href = 'index.html';
}

// Get stored users
function getStoredUsers() {
    const users = localStorage.getItem('registeredUsers');
    return users ? JSON.parse(users) : [];
}

// Generate unique user ID
function generateUserId() {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// Simple password hashing (for demo purposes only)
function hashPassword(password) {
    // In a real application, use proper hashing like bcrypt
    let hash = 0;
    for (let i = 0; i < password.length; i++) {
        const char = password.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
}

// Remove sensitive data from user object
function sanitizeUser(user) {
    const sanitized = { ...user };
    delete sanitized.password;
    return sanitized;
}

// Session management
function getSessionInfo() {
    if (!isLoggedIn()) {
        return null;
    }

    return {
        user: getCurrentUser(),
        loginTime: localStorage.getItem('loginTime'),
        sessionDuration: getSessionDuration()
    };
}

function getSessionDuration() {
    const loginTime = localStorage.getItem('loginTime');
    if (!loginTime) return 0;

    const now = new Date();
    const login = new Date(loginTime);
    return Math.floor((now - login) / 1000 / 60); // Duration in minutes
}

// Check if session is expired (24 hours)
function isSessionExpired() {
    const sessionDuration = getSessionDuration();
    return sessionDuration > (24 * 60); // 24 hours in minutes
}

// Refresh session
function refreshSession() {
    if (isLoggedIn() && !isSessionExpired()) {
        localStorage.setItem('loginTime', new Date().toISOString());
        return true;
    }
    return false;
}

// Auto-logout on session expiry
function checkSessionExpiry() {
    if (isLoggedIn() && isSessionExpired()) {
        alert('Your session has expired. Please log in again.');
        logoutUser();
    }
}

// Password validation
function validatePasswordStrength(password) {
    const criteria = {
        length: password.length >= 8,
        lowercase: /[a-z]/.test(password),
        uppercase: /[A-Z]/.test(password),
        number: /\d/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };

    const score = Object.values(criteria).filter(Boolean).length;

    return {
        score,
        criteria,
        strength: score < 3 ? 'weak' : score < 5 ? 'medium' : 'strong'
    };
}

// Form validation helpers
function validateRegistrationForm(formData) {
    const errors = [];

    if (!formData.fullName || formData.fullName.trim().length < 2) {
        errors.push('Full name must be at least 2 characters');
    }

    if (!formData.email || !validateEmail(formData.email)) {
        errors.push('Please enter a valid email address');
    }

    if (!formData.password || formData.password.length < 6) {
        errors.push('Password must be at least 6 characters');
    }

    if (formData.password !== formData.confirmPassword) {
        errors.push('Passwords do not match');
    }

    if (formData.dueDate) {
        const dueDate = new Date(formData.dueDate);
        const today = new Date();
        const maxDate = new Date();
        maxDate.setMonth(maxDate.getMonth() + 10);

        if (dueDate < today) {
            errors.push('Due date cannot be in the past');
        } else if (dueDate > maxDate) {
            errors.push('Due date seems too far in the future');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

function validateLoginForm(formData) {
    const errors = [];

    if (!formData.email || !validateEmail(formData.email)) {
        errors.push('Please enter a valid email address');
    }

    if (!formData.password) {
        errors.push('Password is required');
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

// User profile management
function updateUserProfile(updates) {
    const currentUser = getCurrentUser();
    if (!currentUser) return false;

    const updatedUser = { ...currentUser, ...updates };
    localStorage.setItem('currentUser', JSON.stringify(updatedUser));

    // Update in registered users list if not demo user
    if (currentUser.id !== 'demo') {
        const users = getStoredUsers();
        const userIndex = users.findIndex(u => u.id === currentUser.id);
        if (userIndex !== -1) {
            users[userIndex] = { ...users[userIndex], ...updates };
            localStorage.setItem('registeredUsers', JSON.stringify(users));
        }
    }

    return true;
}

// Initialize session checking
setInterval(checkSessionExpiry, 60000); // Check every minute

// Export functions for global use
window.authFunctions = {
    registerUser,
    loginUser,
    logoutUser,
    getSessionInfo,
    refreshSession,
    validatePasswordStrength,
    validateRegistrationForm,
    validateLoginForm,
    updateUserProfile
};
