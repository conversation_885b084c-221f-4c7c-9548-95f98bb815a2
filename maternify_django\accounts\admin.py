from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import CustomUser, PregnancyRecord, HealthCalculation


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """Admin configuration for CustomUser"""
    list_display = ('username', 'email', 'full_name', 'is_pregnant', 'due_date', 'date_joined')
    list_filter = ('is_pregnant', 'is_staff', 'is_active', 'date_joined')
    search_fields = ('username', 'email', 'full_name')
    ordering = ('-date_joined',)
    
    fieldsets = UserAdmin.fieldsets + (
        ('Personal Information', {
            'fields': ('full_name', 'phone_number', 'date_of_birth', 'profile_picture')
        }),
        ('Emergency Contact', {
            'fields': ('emergency_contact', 'emergency_phone')
        }),
        ('Pregnancy Information', {
            'fields': ('is_pregnant', 'due_date', 'pregnancy_week', 'pre_pregnancy_weight', 'current_weight', 'height')
        }),
        ('Preferences', {
            'fields': ('email_notifications', 'sms_notifications')
        }),
    )
    
    add_fieldsets = UserAdmin.add_fieldsets + (
        ('Personal Information', {
            'fields': ('full_name', 'email')
        }),
    )


@admin.register(PregnancyRecord)
class PregnancyRecordAdmin(admin.ModelAdmin):
    """Admin configuration for PregnancyRecord"""
    list_display = ('user', 'date_recorded', 'weight', 'blood_pressure_systolic', 'blood_pressure_diastolic', 'doctor_visit')
    list_filter = ('doctor_visit', 'date_recorded')
    search_fields = ('user__username', 'user__email')
    ordering = ('-date_recorded',)
    date_hierarchy = 'date_recorded'


@admin.register(HealthCalculation)
class HealthCalculationAdmin(admin.ModelAdmin):
    """Admin configuration for HealthCalculation"""
    list_display = ('user', 'calculation_type', 'created_at')
    list_filter = ('calculation_type', 'created_at')
    search_fields = ('user__username', 'user__email')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    readonly_fields = ('input_data', 'result_data')
