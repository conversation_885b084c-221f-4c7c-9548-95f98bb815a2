<?php
/**
 * Forum Reply Model for MATERNIFY PHP Backend
 */

class ForumReply {
    private $conn;
    private $table_name = "forum_replies";

    public $id;
    public $post_id;
    public $user_id;
    public $content;
    public $is_anonymous;
    public $likes;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Create forum reply
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET post_id=:post_id, user_id=:user_id, content=:content, 
                      is_anonymous=:is_anonymous";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->content = htmlspecialchars(strip_tags($this->content));

        // Bind values
        $stmt->bindParam(":post_id", $this->post_id);
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":content", $this->content);
        $stmt->bindParam(":is_anonymous", $this->is_anonymous);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Read all replies for a post
    public function readByPost($post_id) {
        $query = "SELECT fr.*, u.username, u.full_name
                  FROM " . $this->table_name . " fr
                  LEFT JOIN users u ON fr.user_id = u.id
                  WHERE fr.post_id = :post_id
                  ORDER BY fr.created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":post_id", $post_id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Read single reply
    public function readOne() {
        $query = "SELECT fr.*, u.username, u.full_name
                  FROM " . $this->table_name . " fr
                  LEFT JOIN users u ON fr.user_id = u.id
                  WHERE fr.id = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->post_id = $row['post_id'];
            $this->user_id = $row['user_id'];
            $this->content = $row['content'];
            $this->is_anonymous = $row['is_anonymous'];
            $this->likes = $row['likes'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return $row;
        }

        return false;
    }

    // Update reply
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                  SET content=:content, updated_at=CURRENT_TIMESTAMP
                  WHERE id=:id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->content = htmlspecialchars(strip_tags($this->content));

        // Bind values
        $stmt->bindParam(":content", $this->content);
        $stmt->bindParam(":id", $this->id);

        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // Delete reply
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);

        if($stmt->execute()) {
            return true;
        }

        return false;
    }

    // Increment likes
    public function incrementLikes() {
        $query = "UPDATE " . $this->table_name . " 
                  SET likes = likes + 1 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Get author display name
    public function getAuthorDisplay($row) {
        if ($row['is_anonymous']) {
            return "Anonymous";
        }
        return $row['full_name'] ?: $row['username'];
    }

    // Convert to array for JSON response
    public function toArray($row) {
        return [
            'id' => (int)$row['id'],
            'post_id' => (int)$row['post_id'],
            'content' => $row['content'],
            'author' => $this->getAuthorDisplay($row),
            'is_anonymous' => (bool)$row['is_anonymous'],
            'likes' => (int)$row['likes'],
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at']
        ];
    }

    // Get recent replies by user
    public function getRecentByUser($user_id, $limit = 10) {
        $query = "SELECT fr.*, fp.title as post_title
                  FROM " . $this->table_name . " fr
                  LEFT JOIN forum_posts fp ON fr.post_id = fp.id
                  WHERE fr.user_id = :user_id
                  ORDER BY fr.created_at DESC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Count replies by user
    public function countByUser($user_id) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE user_id = :user_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":user_id", $user_id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$row['count'];
    }

    // Get reply statistics
    public function getStats() {
        $query = "SELECT 
                    COUNT(*) as total_replies,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(DISTINCT post_id) as posts_with_replies,
                    AVG(likes) as avg_likes
                  FROM " . $this->table_name;

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    // Search replies
    public function search($searchTerm, $post_id = null, $limit = 20) {
        $query = "SELECT fr.*, u.username, u.full_name, fp.title as post_title
                  FROM " . $this->table_name . " fr
                  LEFT JOIN users u ON fr.user_id = u.id
                  LEFT JOIN forum_posts fp ON fr.post_id = fp.id
                  WHERE fr.content LIKE :search";

        if ($post_id) {
            $query .= " AND fr.post_id = :post_id";
        }

        $query .= " ORDER BY fr.created_at DESC LIMIT :limit";

        $stmt = $this->conn->prepare($query);

        $searchTerm = "%{$searchTerm}%";
        $stmt->bindParam(":search", $searchTerm);

        if ($post_id) {
            $stmt->bindParam(":post_id", $post_id);
        }

        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
