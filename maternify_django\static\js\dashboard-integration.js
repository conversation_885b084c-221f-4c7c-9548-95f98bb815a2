/**
 * Dashboard Integration JavaScript
 * Supports both Django and PHP backends
 */

// Configuration
const CONFIG = {
    // Backend type: 'django' or 'php'
    BACKEND_TYPE: 'django', // Change to 'php' to use PHP backend
    
    // API endpoints
    DJANGO_BASE_URL: window.location.origin,
    PHP_BASE_URL: 'http://localhost/maternify/php_backend', // Update with your PHP backend URL
    
    // API paths
    ENDPOINTS: {
        django: {
            dashboard: '/api/dashboard/',
            auth: '/accounts/',
            health_tools: '/api/'
        },
        php: {
            dashboard: '/api/dashboard.php',
            auth: '/api/auth.php',
            health_tools: '/api/health_tools.php'
        }
    }
};

// API Client
class APIClient {
    constructor() {
        this.backendType = CONFIG.BACKEND_TYPE;
        this.baseURL = this.backendType === 'django' ? CONFIG.DJANGO_BASE_URL : CONFIG.PHP_BASE_URL;
        this.endpoints = CONFIG.ENDPOINTS[this.backendType];
    }

    // Get authentication token
    getToken() {
        return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    }

    // Set authentication token
    setToken(token) {
        localStorage.setItem('auth_token', token);
    }

    // Remove authentication token
    removeToken() {
        localStorage.removeItem('auth_token');
        sessionStorage.removeItem('auth_token');
    }

    // Make authenticated API request
    async makeRequest(endpoint, options = {}) {
        const token = this.getToken();
        const url = `${this.baseURL}${endpoint}`;
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            }
        };

        // Add CSRF token for Django
        if (this.backendType === 'django') {
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (csrfToken) {
                defaultOptions.headers['X-CSRFToken'] = csrfToken.value;
            }
        }

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || data.error || 'Request failed');
            }
            
            return data;
        } catch (error) {
            console.error('API Request failed:', error);
            throw error;
        }
    }

    // Dashboard API methods
    async getDashboardData() {
        return await this.makeRequest(this.endpoints.dashboard);
    }

    // Authentication API methods
    async login(credentials) {
        const endpoint = this.backendType === 'django' 
            ? `${this.endpoints.auth}login/`
            : `${this.endpoints.auth}/login`;
            
        const response = await this.makeRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify(credentials)
        });
        
        if (response.token) {
            this.setToken(response.token);
        }
        
        return response;
    }

    async logout() {
        const endpoint = this.backendType === 'django' 
            ? `${this.endpoints.auth}logout/`
            : `${this.endpoints.auth}/logout`;
            
        try {
            await this.makeRequest(endpoint, { method: 'POST' });
        } finally {
            this.removeToken();
        }
    }

    // Health tools API methods
    async calculateBMI(data) {
        const endpoint = this.backendType === 'django' 
            ? `${this.endpoints.health_tools}calculate-bmi/`
            : `${this.endpoints.health_tools}/bmi`;
            
        return await this.makeRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async calculateDueDate(data) {
        const endpoint = this.backendType === 'django' 
            ? `${this.endpoints.health_tools}calculate-due-date/`
            : `${this.endpoints.health_tools}/due-date`;
            
        return await this.makeRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async trackBloodPressure(data) {
        const endpoint = this.backendType === 'django' 
            ? `${this.endpoints.health_tools}track-blood-pressure/`
            : `${this.endpoints.health_tools}/blood-pressure`;
            
        return await this.makeRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    async calculateNutrition(data) {
        const endpoint = this.backendType === 'django' 
            ? `${this.endpoints.health_tools}calculate-nutrition/`
            : `${this.endpoints.health_tools}/nutrition`;
            
        return await this.makeRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }
}

// Dashboard Manager
class DashboardManager {
    constructor() {
        this.api = new APIClient();
        this.dashboardData = null;
    }

    // Initialize dashboard
    async initialize() {
        try {
            await this.loadDashboardData();
            this.renderDashboard();
            this.setupEventListeners();
        } catch (error) {
            console.error('Dashboard initialization failed:', error);
            this.handleError(error);
        }
    }

    // Load dashboard data from API
    async loadDashboardData() {
        try {
            const response = await this.api.getDashboardData();
            this.dashboardData = response.data || response;
        } catch (error) {
            if (error.message.includes('401') || error.message.includes('Authentication')) {
                // Redirect to login if not authenticated
                window.location.href = '/accounts/login/';
                return;
            }
            throw error;
        }
    }

    // Render dashboard with data
    renderDashboard() {
        if (!this.dashboardData) return;

        const { user, pregnancy_stats, daily_tips, recent_activity, upcoming_reminders } = this.dashboardData;

        // Update user greeting
        this.updateElement('userGreeting', `Hi, ${user.name || user.username}!`);
        this.updateElement('welcomeTitle', `Welcome back, ${user.name || user.username}!`);

        // Update pregnancy stats
        if (pregnancy_stats) {
            this.updateElement('daysToGo', pregnancy_stats.days_to_go);
            this.updateElement('currentWeek', pregnancy_stats.current_week);
            this.updateElement('trimester', pregnancy_stats.trimester);
        }

        // Update daily tips
        if (daily_tips) {
            this.updateElement('nutritionTip', daily_tips.nutrition);
            this.updateElement('exerciseTip', daily_tips.exercise);
            this.updateElement('wellnessTip', daily_tips.wellness);
        }

        // Update recent activity
        if (recent_activity) {
            this.renderRecentActivity(recent_activity);
        }

        // Update upcoming reminders
        if (upcoming_reminders) {
            this.renderUpcomingReminders(upcoming_reminders);
        }

        // Update join date
        if (user.date_joined) {
            this.updateElement('joinDate', this.formatTimeAgo(user.date_joined));
        }
    }

    // Update element content safely
    updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        }
    }

    // Render recent activity list
    renderRecentActivity(activities) {
        const container = document.querySelector('.activity-list');
        if (!container) return;

        container.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <h4>${activity.title}</h4>
                    <p>${activity.description}</p>
                    <span class="activity-time">${this.formatTimeAgo(activity.time)}</span>
                </div>
            </div>
        `).join('');
    }

    // Render upcoming reminders
    renderUpcomingReminders(reminders) {
        const container = document.querySelector('.reminders-list');
        if (!container) return;

        container.innerHTML = reminders.map(reminder => {
            const date = new Date(reminder.date);
            return `
                <div class="reminder-item">
                    <div class="reminder-date">
                        <span class="day">${date.getDate()}</span>
                        <span class="month">${date.toLocaleDateString('en', { month: 'short' })}</span>
                    </div>
                    <div class="reminder-content">
                        <h4>${reminder.title}</h4>
                        <p>${reminder.description}</p>
                        <span class="reminder-time">${reminder.time}</span>
                    </div>
                    <div class="reminder-action">
                        <button class="btn-small">${reminder.type === 'appointment' ? 'View' : 'Remind'}</button>
                    </div>
                </div>
            `;
        }).join('');
    }

    // Format time ago
    formatTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        return `${Math.floor(diffInSeconds / 86400)} days ago`;
    }

    // Setup event listeners
    setupEventListeners() {
        // Refresh dashboard button
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.initialize());
        }

        // Logout functionality
        const logoutBtns = document.querySelectorAll('.logout-btn');
        logoutBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogout();
            });
        });
    }

    // Handle logout
    async handleLogout() {
        try {
            await this.api.logout();
            window.location.href = '/';
        } catch (error) {
            console.error('Logout failed:', error);
            // Force logout even if API call fails
            this.api.removeToken();
            window.location.href = '/';
        }
    }

    // Handle errors
    handleError(error) {
        console.error('Dashboard error:', error);
        
        // Show user-friendly error message
        const errorContainer = document.getElementById('errorContainer');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>Unable to load dashboard data. Please try refreshing the page.</span>
                    <button onclick="location.reload()" class="btn btn-small">Refresh</button>
                </div>
            `;
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the dashboard page
    if (document.querySelector('.dashboard-main')) {
        const dashboard = new DashboardManager();
        dashboard.initialize();
        
        // Make dashboard manager globally available
        window.dashboardManager = dashboard;
        window.apiClient = dashboard.api;
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APIClient, DashboardManager };
}
