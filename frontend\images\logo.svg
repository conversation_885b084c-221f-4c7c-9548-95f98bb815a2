<svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a8d8ea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#aa96da;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="heartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffb3d9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff99cc;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background circle -->
  <circle cx="40" cy="40" r="38" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="2"/>

  <!-- Mother and baby silhouette -->
  <!-- Mother's head -->
  <circle cx="35" cy="25" r="8" fill="url(#heartGradient)"/>

  <!-- Mother's body -->
  <ellipse cx="35" cy="40" rx="6" ry="12" fill="url(#heartGradient)"/>

  <!-- Mother's arms (protective gesture) -->
  <ellipse cx="28" cy="35" rx="3" ry="8" fill="url(#heartGradient)" transform="rotate(-20 28 35)"/>
  <ellipse cx="42" cy="35" rx="3" ry="8" fill="url(#heartGradient)" transform="rotate(20 42 35)"/>

  <!-- Baby in mother's arms -->
  <circle cx="40" cy="42" r="4" fill="#ffffff"/>
  <ellipse cx="40" cy="47" rx="2.5" ry="4" fill="#ffffff"/>

  <!-- Heart symbol above -->
  <path d="M 40 15 C 37 12, 32 12, 32 17 C 32 12, 27 12, 30 15 C 30 18, 35 22, 40 26 C 45 22, 50 18, 50 15 C 53 12, 48 12, 48 17 C 48 12, 43 12, 40 15 Z" fill="#ffffff" opacity="0.9"/>

  <!-- Decorative dots -->
  <circle cx="20" cy="25" r="1.5" fill="#ffffff" opacity="0.7"/>
  <circle cx="60" cy="30" r="1" fill="#ffffff" opacity="0.7"/>
  <circle cx="25" cy="55" r="1" fill="#ffffff" opacity="0.7"/>
  <circle cx="55" cy="50" r="1.5" fill="#ffffff" opacity="0.7"/>
</svg>
