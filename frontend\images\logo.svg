<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
  <!-- Baby figure based on the provided image -->
  <!-- Head -->
  <circle cx="30" cy="18" r="10" fill="#ff6b9d"/>

  <!-- Body/Torso -->
  <path d="M 18 28 Q 18 25 22 25 L 38 25 Q 42 25 42 28 L 42 38 Q 42 42 38 42 L 22 42 Q 18 42 18 38 Z" fill="#ff6b9d"/>

  <!-- Arms spread wide -->
  <ellipse cx="10" cy="32" rx="6" ry="10" fill="#ff6b9d" transform="rotate(-25 10 32)"/>
  <ellipse cx="50" cy="32" rx="6" ry="10" fill="#ff6b9d" transform="rotate(25 50 32)"/>

  <!-- Legs/Feet -->
  <path d="M 20 45 Q 18 45 18 47 L 18 52 Q 18 55 20 55 L 26 55 Q 28 55 28 52 L 28 47 Q 28 45 26 45 Z" fill="#ff6b9d"/>
  <path d="M 32 45 Q 30 45 30 47 L 30 52 Q 30 55 32 55 L 38 55 Q 40 55 40 52 L 40 47 Q 40 45 38 45 Z" fill="#ff6b9d"/>
</svg>
