<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg">
  <!-- Baby/Child silhouette based on provided image -->
  <!-- Light background -->
  <rect width="60" height="60" fill="#f8f9fa" rx="8"/>

  <!-- Baby head -->
  <circle cx="30" cy="18" r="9" fill="#ff6b9d"/>

  <!-- Baby body/torso -->
  <path d="M 20 28 Q 20 26 22 26 L 38 26 Q 40 26 40 28 L 40 36 Q 40 38 38 38 L 22 38 Q 20 38 20 36 Z" fill="#ff6b9d"/>

  <!-- Baby arms spread wide (happy gesture) -->
  <ellipse cx="12" cy="32" rx="4" ry="8" fill="#ff6b9d" transform="rotate(-30 12 32)"/>
  <ellipse cx="48" cy="32" rx="4" ry="8" fill="#ff6b9d" transform="rotate(30 48 32)"/>

  <!-- Baby legs/feet -->
  <path d="M 22 40 Q 20 40 20 42 L 20 48 Q 20 50 22 50 L 26 50 Q 28 50 28 48 L 28 42 Q 28 40 26 40 Z" fill="#ff6b9d"/>
  <path d="M 32 40 Q 30 40 30 42 L 30 48 Q 30 50 32 50 L 36 50 Q 38 50 38 48 L 38 42 Q 38 40 36 40 Z" fill="#ff6b9d"/>
</svg>
