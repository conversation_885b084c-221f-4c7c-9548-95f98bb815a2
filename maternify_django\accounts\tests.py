from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from .models import PregnancyRecord, HealthCalculation

User = get_user_model()


class CustomUserModelTest(TestCase):
    """Test CustomUser model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            full_name='Test User'
        )
    
    def test_user_creation(self):
        self.assertEqual(self.user.username, 'testuser')
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertEqual(self.user.full_name, 'Test User')
        self.assertEqual(self.user.display_name, 'Test User')
    
    def test_user_str(self):
        self.assertEqual(str(self.user), 'testuser')


class AccountsViewsTest(TestCase):
    """Test accounts views"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_login_view(self):
        response = self.client.get(reverse('accounts:login'))
        self.assertEqual(response.status_code, 200)
    
    def test_register_view(self):
        response = self.client.get(reverse('accounts:register'))
        self.assertEqual(response.status_code, 200)
    
    def test_profile_view_requires_login(self):
        response = self.client.get(reverse('accounts:profile'))
        self.assertRedirects(response, '/accounts/login/?next=/accounts/profile/')
    
    def test_profile_view_authenticated(self):
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('accounts:profile'))
        self.assertEqual(response.status_code, 200)
