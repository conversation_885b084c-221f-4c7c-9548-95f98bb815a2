// Main JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        }));
    }

    // Check authentication status and update navigation
    updateNavigation();

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Authentication helper functions
function isLoggedIn() {
    return localStorage.getItem('userLoggedIn') === 'true';
}

function getCurrentUser() {
    const userData = localStorage.getItem('currentUser');
    return userData ? JSON.parse(userData) : null;
}

function updateNavigation() {
    const navMenu = document.querySelector('.nav-menu');
    if (!navMenu) return;

    if (isLoggedIn()) {
        const user = getCurrentUser();
        // Update navigation for logged-in users
        navMenu.innerHTML = `
            <li class="nav-item">
                <a href="dashboard.html" class="nav-link">Dashboard</a>
            </li>
            <li class="nav-item">
                <a href="pregnancy-care.html" class="nav-link">Pregnancy Care</a>
            </li>
            <li class="nav-item">
                <a href="infant-care.html" class="nav-link">Infant Care</a>
            </li>
            <li class="nav-item">
                <a href="tools.html" class="nav-link">Tools</a>
            </li>
            <li class="nav-item">
                <a href="forum.html" class="nav-link">Forum</a>
            </li>
            <li class="nav-item">
                <a href="resources.html" class="nav-link">Resources</a>
            </li>
            <li class="nav-item">
                <span class="nav-link user-greeting">Hi, ${user?.name || 'User'}!</span>
            </li>
            <li class="nav-item">
                <a href="#" class="nav-link logout-btn" onclick="logout()">Logout</a>
            </li>
        `;
    }
}

function logout() {
    localStorage.removeItem('userLoggedIn');
    localStorage.removeItem('currentUser');
    window.location.href = 'index.html';
}

// Access control for protected pages
function checkAccess() {
    const protectedPages = [
        'dashboard.html',
        'pregnancy-care.html',
        'infant-care.html',
        'tools.html',
        'forum.html',
        'resources.html'
    ];

    const currentPage = window.location.pathname.split('/').pop();

    if (protectedPages.includes(currentPage) && !isLoggedIn()) {
        alert('Please log in to access this page.');
        window.location.href = 'login.html';
        return false;
    }
    return true;
}

// Initialize access control on page load
document.addEventListener('DOMContentLoaded', function() {
    checkAccess();
});

// Utility functions
function showAlert(message, type = 'info') {
    // Check if tools.js showAlert exists and use it instead
    if (window.toolsFunctions && window.toolsFunctions.showAlert) {
        return window.toolsFunctions.showAlert(message, type);
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        <span>${message}</span>
        <button class="alert-close" onclick="this.parentElement.remove()">&times;</button>
    `;

    // Insert at the top of the page
    document.body.insertBefore(alertDiv, document.body.firstChild);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}

function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function validatePassword(password) {
    // At least 6 characters
    return password.length >= 6;
}

// Form validation helper
function validateForm(formData, rules) {
    const errors = [];

    for (const field in rules) {
        const value = formData[field];
        const rule = rules[field];

        if (rule.required && (!value || value.trim() === '')) {
            errors.push(`${rule.label} is required`);
        }

        if (value && rule.minLength && value.length < rule.minLength) {
            errors.push(`${rule.label} must be at least ${rule.minLength} characters`);
        }

        if (value && rule.email && !validateEmail(value)) {
            errors.push(`${rule.label} must be a valid email address`);
        }

        if (value && rule.match && value !== formData[rule.match]) {
            errors.push(`${rule.label} must match ${rules[rule.match].label}`);
        }
    }

    return errors;
}

// Loading spinner utility
function showLoading(element) {
    const originalText = element.innerHTML;
    element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
    element.disabled = true;

    return function hideLoading() {
        element.innerHTML = originalText;
        element.disabled = false;
    };
}

// Date formatting utility
function formatDate(date) {
    return new Date(date).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Calculate age utility
function calculateAge(birthDate) {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }

    return age;
}
