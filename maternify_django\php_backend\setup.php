<?php
/**
 * Setup Script for MATERNIFY PHP Backend
 * Run this file to create database tables and initial setup
 */

include_once 'config/database.php';

echo "<h1>MATERNIFY PHP Backend Setup</h1>\n";

$database = new Database();
$db = $database->getConnection();

if ($db === null) {
    echo "<p style='color: red;'>❌ Database connection failed!</p>\n";
    echo "<p>Please check your database configuration in config/database.php</p>\n";
    exit();
}

echo "<p style='color: green;'>✅ Database connection successful!</p>\n";

// Create tables
echo "<h2>Creating Database Tables...</h2>\n";

if ($database->createTables()) {
    echo "<p style='color: green;'>✅ Database tables created successfully!</p>\n";
} else {
    echo "<p style='color: red;'>❌ Failed to create database tables!</p>\n";
    exit();
}

// Create sample data
echo "<h2>Creating Sample Data...</h2>\n";

try {
    // Create sample user
    $stmt = $db->prepare("INSERT IGNORE INTO users (username, email, password_hash, full_name, is_pregnant, due_date) VALUES (?, ?, ?, ?, ?, ?)");
    
    $sample_users = [
        [
            'username' => 'demo_user',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('demo123', PASSWORD_DEFAULT),
            'full_name' => 'Demo User',
            'is_pregnant' => true,
            'due_date' => date('Y-m-d', strtotime('+120 days'))
        ],
        [
            'username' => 'jane_doe',
            'email' => '<EMAIL>',
            'password_hash' => password_hash('password123', PASSWORD_DEFAULT),
            'full_name' => 'Jane Doe',
            'is_pregnant' => false,
            'due_date' => null
        ]
    ];

    foreach ($sample_users as $user) {
        $stmt->execute([
            $user['username'],
            $user['email'],
            $user['password_hash'],
            $user['full_name'],
            $user['is_pregnant'],
            $user['due_date']
        ]);
    }

    echo "<p style='color: green;'>✅ Sample users created!</p>\n";

    // Create sample health calculations
    $stmt = $db->prepare("INSERT IGNORE INTO health_calculations (user_id, calculation_type, input_data, result_data) VALUES (?, ?, ?, ?)");
    
    $sample_calculations = [
        [
            'user_id' => 1,
            'calculation_type' => 'bmi',
            'input_data' => json_encode(['height' => 165, 'weight' => 60, 'age' => 28]),
            'result_data' => json_encode(['bmi' => 22.0, 'category' => 'Normal weight'])
        ],
        [
            'user_id' => 1,
            'calculation_type' => 'due_date',
            'input_data' => json_encode(['lmp_date' => date('Y-m-d', strtotime('-160 days'))]),
            'result_data' => json_encode(['due_date' => date('Y-m-d', strtotime('+120 days')), 'weeks_pregnant' => 23])
        ]
    ];

    foreach ($sample_calculations as $calc) {
        $stmt->execute([
            $calc['user_id'],
            $calc['calculation_type'],
            $calc['input_data'],
            $calc['result_data']
        ]);
    }

    echo "<p style='color: green;'>✅ Sample health calculations created!</p>\n";

    // Create sample appointments
    $stmt = $db->prepare("INSERT IGNORE INTO appointments (user_id, title, description, appointment_date, doctor_name) VALUES (?, ?, ?, ?, ?)");
    
    $sample_appointments = [
        [
            'user_id' => 1,
            'title' => 'Prenatal Checkup',
            'description' => 'Regular prenatal appointment with Dr. Smith',
            'appointment_date' => date('Y-m-d H:i:s', strtotime('+7 days 14:00')),
            'doctor_name' => 'Dr. Sarah Smith'
        ],
        [
            'user_id' => 1,
            'title' => 'Ultrasound Scan',
            'description' => '20-week anatomy scan',
            'appointment_date' => date('Y-m-d H:i:s', strtotime('+14 days 10:30')),
            'doctor_name' => 'Dr. Michael Johnson'
        ]
    ];

    foreach ($sample_appointments as $appointment) {
        $stmt->execute([
            $appointment['user_id'],
            $appointment['title'],
            $appointment['description'],
            $appointment['appointment_date'],
            $appointment['doctor_name']
        ]);
    }

    echo "<p style='color: green;'>✅ Sample appointments created!</p>\n";

    // Create sample forum posts
    $stmt = $db->prepare("INSERT IGNORE INTO forum_posts (user_id, title, content, category, is_anonymous, views, likes) VALUES (?, ?, ?, ?, ?, ?, ?)");

    $sample_posts = [
        [
            'user_id' => 1,
            'title' => 'First Trimester Tips - What to Expect',
            'content' => 'I\'m in my first trimester and looking for advice on nutrition, exercise, and managing morning sickness. What are some essential tips that helped you during this time?',
            'category' => 'pregnancy',
            'is_anonymous' => false,
            'views' => 45,
            'likes' => 12
        ],
        [
            'user_id' => 2,
            'title' => 'Breastfeeding Challenges - Need Support',
            'content' => 'I\'m having trouble with breastfeeding and feeling overwhelmed. Any tips for improving latch and increasing milk supply? How long did it take for you to feel comfortable?',
            'category' => 'feeding',
            'is_anonymous' => false,
            'views' => 38,
            'likes' => 8
        ],
        [
            'user_id' => 1,
            'title' => 'Newborn Sleep Schedule Help',
            'content' => 'My 2-week-old seems to have day and night confused. Any advice on establishing a good sleep routine? When did your baby start sleeping longer stretches?',
            'category' => 'newborn',
            'is_anonymous' => false,
            'views' => 52,
            'likes' => 15
        ],
        [
            'user_id' => 2,
            'title' => 'Postpartum Anxiety - Is This Normal?',
            'content' => 'I\'m experiencing a lot of anxiety since giving birth. Is this normal? When should I seek help? I feel like I\'m not bonding with my baby the way I should.',
            'category' => 'support',
            'is_anonymous' => true,
            'views' => 67,
            'likes' => 23
        ],
        [
            'user_id' => 1,
            'title' => 'Exercise During Pregnancy - Safe Options',
            'content' => 'What exercises are safe during pregnancy? I used to run regularly but now I\'m not sure what\'s appropriate. Any recommendations for prenatal fitness?',
            'category' => 'health',
            'is_anonymous' => false,
            'views' => 29,
            'likes' => 6
        ],
        [
            'user_id' => 2,
            'title' => 'Introduction - New Mom Here!',
            'content' => 'Hi everyone! I just joined this community and wanted to introduce myself. I\'m a first-time mom with a 3-month-old baby. Looking forward to connecting with other moms!',
            'category' => 'general',
            'is_anonymous' => false,
            'views' => 34,
            'likes' => 18
        ]
    ];

    foreach ($sample_posts as $post) {
        $stmt->execute([
            $post['user_id'],
            $post['title'],
            $post['content'],
            $post['category'],
            $post['is_anonymous'],
            $post['views'],
            $post['likes']
        ]);
    }

    echo "<p style='color: green;'>✅ Sample forum posts created!</p>\n";

    // Create sample forum replies
    $stmt = $db->prepare("INSERT IGNORE INTO forum_replies (post_id, user_id, content, is_anonymous, likes) VALUES (?, ?, ?, ?, ?)");

    $sample_replies = [
        [
            'post_id' => 1,
            'user_id' => 2,
            'content' => 'Great question! For morning sickness, I found that eating small, frequent meals helped a lot. Ginger tea was also a lifesaver for me.',
            'is_anonymous' => false,
            'likes' => 5
        ],
        [
            'post_id' => 1,
            'user_id' => 1,
            'content' => 'Thank you for the advice! I\'ll definitely try the ginger tea. Did you have any specific foods that helped or made it worse?',
            'is_anonymous' => false,
            'likes' => 2
        ],
        [
            'post_id' => 2,
            'user_id' => 1,
            'content' => 'I had similar challenges! It took about 6 weeks for us to get the hang of it. Don\'t give up - it does get easier. Consider seeing a lactation consultant if you haven\'t already.',
            'is_anonymous' => false,
            'likes' => 7
        ],
        [
            'post_id' => 4,
            'user_id' => 1,
            'content' => 'You\'re not alone in feeling this way. Postpartum anxiety is very common. Please don\'t hesitate to talk to your doctor about it. There\'s help available and you deserve support.',
            'is_anonymous' => false,
            'likes' => 12
        ]
    ];

    foreach ($sample_replies as $reply) {
        $stmt->execute([
            $reply['post_id'],
            $reply['user_id'],
            $reply['content'],
            $reply['is_anonymous'],
            $reply['likes']
        ]);
    }

    echo "<p style='color: green;'>✅ Sample forum replies created!</p>\n";

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error creating sample data: " . $e->getMessage() . "</p>\n";
}

echo "<h2>Setup Complete!</h2>\n";
echo "<div style='background: #f0f8ff; padding: 20px; border-radius: 5px; margin: 20px 0;'>\n";
echo "<h3>🎉 MATERNIFY PHP Backend Setup Successful!</h3>\n";
echo "<p><strong>Sample Login Credentials:</strong></p>\n";
echo "<ul>\n";
echo "<li><strong>Username:</strong> demo_user</li>\n";
echo "<li><strong>Password:</strong> demo123</li>\n";
echo "</ul>\n";
echo "<p><strong>API Endpoints:</strong></p>\n";
echo "<ul>\n";
echo "<li><strong>Authentication:</strong> /api/auth.php</li>\n";
echo "<li><strong>Dashboard:</strong> /api/dashboard.php</li>\n";
echo "<li><strong>Health Tools:</strong> /api/health_tools.php</li>\n";
echo "</ul>\n";
echo "<p><strong>Next Steps:</strong></p>\n";
echo "<ol>\n";
echo "<li>Configure your web server to serve the PHP files</li>\n";
echo "<li>Update database credentials in config/database.php if needed</li>\n";
echo "<li>Test the API endpoints using a tool like Postman</li>\n";
echo "<li>Integrate with your frontend application</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<h3>API Usage Examples:</h3>\n";
echo "<pre style='background: #f5f5f5; padding: 15px; border-radius: 5px;'>\n";
echo "// Login\n";
echo "POST /api/auth.php/login\n";
echo "{\n";
echo "  \"username\": \"demo_user\",\n";
echo "  \"password\": \"demo123\"\n";
echo "}\n\n";

echo "// Get Dashboard Data\n";
echo "GET /api/dashboard.php\n";
echo "Authorization: Bearer YOUR_TOKEN\n\n";

echo "// Calculate BMI\n";
echo "POST /api/health_tools.php/bmi\n";
echo "Authorization: Bearer YOUR_TOKEN\n";
echo "{\n";
echo "  \"height\": 165,\n";
echo "  \"weight\": 60,\n";
echo "  \"age\": 28,\n";
echo "  \"activity_level\": \"moderate\"\n";
echo "}\n";
echo "</pre>\n";
?>
