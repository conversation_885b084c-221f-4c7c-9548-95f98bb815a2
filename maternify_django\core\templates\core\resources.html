{% extends 'base.html' %}
{% load static %}

{% block title %}Resources - MATERNIFY{% endblock %}

{% block content %}
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="{% static 'images/logo.svg' %}" alt="MATERNIFY Logo">
                <span>MATERNIFY</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="{% url 'core:dashboard' %}" class="nav-link">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:pregnancy_care' %}" class="nav-link">Pregnancy Care</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:infant_care' %}" class="nav-link">Infant Care</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:tools' %}" class="nav-link">Tools</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:forum' %}" class="nav-link">Community Forum</a>
                </li>
                <li class="nav-item">
                    <a href="{% url 'core:resources' %}" class="nav-link active">Resources</a>
                </li>
                <li class="nav-item">
                    <span class="nav-link user-greeting">Hi, {{ user.display_name }}!</span>
                </li>
                <li class="nav-item">
                    <a href="{% url 'accounts:logout' %}" class="nav-link logout-btn">Logout</a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h1>Educational Resources</h1>
                <p>Videos, guides, and expert content for your journey</p>
            </div>

            <div class="filter-section">
                <form method="get">
                    <select name="category" onchange="this.form.submit()">
                        <option value="">All Categories</option>
                        {% for value, label in categories %}
                            <option value="{{ value }}" {% if value == selected_category %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </form>
            </div>

            <div class="resources-grid">
                {% for resource in resources %}
                <div class="resource-card">
                    <div class="resource-type">{{ resource.get_resource_type_display }}</div>
                    <h3>{{ resource.title }}</h3>
                    <p>{{ resource.description }}</p>
                    {% if resource.external_url %}
                        <a href="{{ resource.external_url }}" target="_blank" class="btn btn-primary">View Resource</a>
                    {% elif resource.file %}
                        <a href="{{ resource.file.url }}" target="_blank" class="btn btn-primary">Download</a>
                    {% endif %}
                </div>
                {% empty %}
                <div class="placeholder-content">
                    <h2>Educational Resources Coming Soon</h2>
                    <p>We're curating the best educational content for you. Check back soon!</p>
                </div>
                {% endfor %}
            </div>

            {% if is_paginated %}
            <div class="pagination">
                <span class="page-links">
                    {% if page_obj.has_previous %}
                        <a href="?page=1&category={{ selected_category }}">&laquo; first</a>
                        <a href="?page={{ page_obj.previous_page_number }}&category={{ selected_category }}">previous</a>
                    {% endif %}

                    <span class="current">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>

                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}&category={{ selected_category }}">next</a>
                        <a href="?page={{ page_obj.paginator.num_pages }}&category={{ selected_category }}">last &raquo;</a>
                    {% endif %}
                </span>
            </div>
            {% endif %}
        </div>
    </main>
{% endblock %}
