<?php
/**
 * Forum Post Model for MATERNIFY PHP Backend
 */

class ForumPost {
    private $conn;
    private $table_name = "forum_posts";

    public $id;
    public $user_id;
    public $title;
    public $content;
    public $category;
    public $is_pinned;
    public $is_anonymous;
    public $views;
    public $likes;
    public $created_at;
    public $updated_at;

    public function __construct($db) {
        $this->conn = $db;
    }

    // Create forum post
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                  SET user_id=:user_id, title=:title, content=:content, 
                      category=:category, is_anonymous=:is_anonymous";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->title = htmlspecialchars(strip_tags($this->title));
        $this->content = htmlspecialchars(strip_tags($this->content));
        $this->category = htmlspecialchars(strip_tags($this->category));

        // Bind values
        $stmt->bindParam(":user_id", $this->user_id);
        $stmt->bindParam(":title", $this->title);
        $stmt->bindParam(":content", $this->content);
        $stmt->bindParam(":category", $this->category);
        $stmt->bindParam(":is_anonymous", $this->is_anonymous);

        if($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }

        return false;
    }

    // Read all posts with filtering and sorting
    public function readAll($category = '', $sort = 'recent', $limit = 20) {
        $query = "SELECT fp.*, u.username, u.full_name,
                         (SELECT COUNT(*) FROM forum_replies fr WHERE fr.post_id = fp.id) as reply_count
                  FROM " . $this->table_name . " fp
                  LEFT JOIN users u ON fp.user_id = u.id";

        // Add category filter
        if (!empty($category)) {
            $query .= " WHERE fp.category = :category";
        }

        // Add sorting
        switch ($sort) {
            case 'popular':
                $query .= " ORDER BY fp.views DESC, fp.likes DESC, fp.created_at DESC";
                break;
            case 'replies':
                $query .= " ORDER BY reply_count DESC, fp.created_at DESC";
                break;
            default: // recent
                $query .= " ORDER BY fp.is_pinned DESC, fp.created_at DESC";
                break;
        }

        $query .= " LIMIT :limit";

        $stmt = $this->conn->prepare($query);

        if (!empty($category)) {
            $stmt->bindParam(":category", $category);
        }
        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Read single post
    public function readOne() {
        $query = "SELECT fp.*, u.username, u.full_name,
                         (SELECT COUNT(*) FROM forum_replies fr WHERE fr.post_id = fp.id) as reply_count
                  FROM " . $this->table_name . " fp
                  LEFT JOIN users u ON fp.user_id = u.id
                  WHERE fp.id = ? LIMIT 0,1";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->user_id = $row['user_id'];
            $this->title = $row['title'];
            $this->content = $row['content'];
            $this->category = $row['category'];
            $this->is_pinned = $row['is_pinned'];
            $this->is_anonymous = $row['is_anonymous'];
            $this->views = $row['views'];
            $this->likes = $row['likes'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return $row;
        }

        return false;
    }

    // Increment view count
    public function incrementViews() {
        $query = "UPDATE " . $this->table_name . " 
                  SET views = views + 1 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Increment likes
    public function incrementLikes() {
        $query = "UPDATE " . $this->table_name . " 
                  SET likes = likes + 1 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    // Get category statistics
    public function getCategoryStats() {
        $categories = [
            'pregnancy' => 'Pregnancy Journey',
            'newborn' => 'Newborn Care',
            'feeding' => 'Feeding & Nutrition',
            'health' => 'Health & Wellness',
            'support' => 'Emotional Support',
            'general' => 'General Discussion'
        ];

        $stats = [];

        foreach ($categories as $code => $name) {
            $query = "SELECT 
                        COUNT(*) as post_count,
                        COUNT(DISTINCT user_id) as member_count
                      FROM " . $this->table_name . " 
                      WHERE category = :category";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":category", $code);
            $stmt->execute();

            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            $stats[$code] = [
                'name' => $name,
                'post_count' => (int)$result['post_count'],
                'member_count' => (int)$result['member_count']
            ];
        }

        return $stats;
    }

    // Get author display name
    public function getAuthorDisplay($row) {
        if ($row['is_anonymous']) {
            return "Anonymous";
        }
        return $row['full_name'] ?: $row['username'];
    }

    // Get category display name
    public function getCategoryDisplay($category) {
        $categories = [
            'pregnancy' => 'Pregnancy Journey',
            'newborn' => 'Newborn Care',
            'feeding' => 'Feeding & Nutrition',
            'health' => 'Health & Wellness',
            'support' => 'Emotional Support',
            'general' => 'General Discussion'
        ];

        return $categories[$category] ?? $category;
    }

    // Convert to array for JSON response
    public function toArray($row) {
        return [
            'id' => (int)$row['id'],
            'title' => $row['title'],
            'content' => $row['content'],
            'category' => $row['category'],
            'category_display' => $this->getCategoryDisplay($row['category']),
            'author' => $this->getAuthorDisplay($row),
            'is_pinned' => (bool)$row['is_pinned'],
            'is_anonymous' => (bool)$row['is_anonymous'],
            'views' => (int)$row['views'],
            'likes' => (int)$row['likes'],
            'reply_count' => (int)$row['reply_count'],
            'created_at' => $row['created_at'],
            'updated_at' => $row['updated_at'],
            'url' => "/forum/post/{$row['id']}/"
        ];
    }

    // Get replies for a post
    public function getReplies() {
        $query = "SELECT fr.*, u.username, u.full_name
                  FROM forum_replies fr
                  LEFT JOIN users u ON fr.user_id = u.id
                  WHERE fr.post_id = :post_id
                  ORDER BY fr.created_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":post_id", $this->id);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // Search posts
    public function search($searchTerm, $category = '', $limit = 20) {
        $query = "SELECT fp.*, u.username, u.full_name,
                         (SELECT COUNT(*) FROM forum_replies fr WHERE fr.post_id = fp.id) as reply_count
                  FROM " . $this->table_name . " fp
                  LEFT JOIN users u ON fp.user_id = u.id
                  WHERE (fp.title LIKE :search OR fp.content LIKE :search)";

        if (!empty($category)) {
            $query .= " AND fp.category = :category";
        }

        $query .= " ORDER BY fp.is_pinned DESC, fp.created_at DESC LIMIT :limit";

        $stmt = $this->conn->prepare($query);

        $searchTerm = "%{$searchTerm}%";
        $stmt->bindParam(":search", $searchTerm);

        if (!empty($category)) {
            $stmt->bindParam(":category", $category);
        }

        $stmt->bindParam(":limit", $limit, PDO::PARAM_INT);

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
