# MATERNIFY PHP Backend

A comprehensive PHP backend API for the MATERNIFY maternal healthcare platform, providing user authentication, health calculations, and dashboard functionality.

## 🚀 Features

### Authentication System
- User registration and login
- JWT token-based authentication
- Password hashing and verification
- Session management

### Dashboard API
- Personalized user dashboard data
- Pregnancy statistics and tracking
- Recent activity timeline
- Health calculation history
- Daily tips and recommendations
- Upcoming appointments and reminders

### Health Tools
- **BMI Calculator**: Enhanced with age, activity level, and pregnancy status
- **Due Date Calculator**: LMP-based pregnancy tracking
- **Blood Pressure Tracker**: Category classification and recommendations
- **Nutrition Calculator**: Daily calorie and macronutrient needs

### Database Models
- Users with pregnancy tracking
- Health calculations with JSON storage
- Health metrics for daily tracking
- Appointments and reminders
- Forum posts and community features

## 📁 Project Structure

```
php_backend/
├── config/
│   └── database.php          # Database configuration and table creation
├── models/
│   ├── User.php             # User model with authentication
│   └── HealthCalculation.php # Health calculation utilities
├── api/
│   ├── auth.php             # Authentication endpoints
│   ├── dashboard.php        # Dashboard data API
│   └── health_tools.php     # Health calculation endpoints
├── setup.php                # Database setup script
└── README.md               # This file
```

## 🛠️ Installation & Setup

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- PDO MySQL extension

### Step 1: Database Configuration
1. Create a MySQL database named `maternify_db`
2. Update database credentials in `config/database.php`:

```php
private $host = 'localhost';
private $db_name = 'maternify_db';
private $username = 'your_username';
private $password = 'your_password';
```

### Step 2: Run Setup Script
Navigate to the PHP backend directory and run:

```bash
php setup.php
```

Or access via web browser:
```
http://your-domain/php_backend/setup.php
```

This will:
- Create all necessary database tables
- Insert sample data
- Create demo user accounts

### Step 3: Configure Web Server
Ensure your web server can serve PHP files and has proper URL rewriting configured.

## 🔐 Authentication

### Register User
```http
POST /api/auth.php/register
Content-Type: application/json

{
  "username": "jane_doe",
  "email": "<EMAIL>",
  "password": "secure_password",
  "full_name": "Jane Doe",
  "is_pregnant": true,
  "due_date": "2024-12-15"
}
```

### Login
```http
POST /api/auth.php/login
Content-Type: application/json

{
  "username": "demo_user",
  "password": "demo123"
}
```

### Verify Token
```http
GET /api/auth.php/verify
Authorization: Bearer YOUR_JWT_TOKEN
```

## 📊 Dashboard API

### Get Dashboard Data
```http
GET /api/dashboard.php
Authorization: Bearer YOUR_JWT_TOKEN
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "name": "Demo User",
      "username": "demo_user",
      "email": "<EMAIL>",
      "date_joined": "2024-01-01 00:00:00"
    },
    "pregnancy_stats": {
      "days_to_go": 120,
      "current_week": 23,
      "trimester": "2nd",
      "due_date": "2024-12-15"
    },
    "recent_calculations": [...],
    "recent_activity": [...],
    "upcoming_reminders": [...],
    "daily_tips": {
      "nutrition": "Include plenty of leafy greens...",
      "exercise": "Take a 20-30 minute walk daily...",
      "wellness": "Practice deep breathing exercises..."
    },
    "stats": {
      "total_calculations": 5,
      "forum_posts": 2
    }
  }
}
```

## 🧮 Health Tools API

### BMI Calculator
```http
POST /api/health_tools.php/bmi
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "height": 165,
  "weight": 60,
  "age": 28,
  "height_unit": "cm",
  "weight_unit": "kg",
  "activity_level": "moderate",
  "pregnancy_status": false
}
```

### Due Date Calculator
```http
POST /api/health_tools.php/due-date
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "lmp_date": "2024-01-01"
}
```

### Blood Pressure Tracker
```http
POST /api/health_tools.php/blood-pressure
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "systolic": 120,
  "diastolic": 80,
  "heart_rate": 72,
  "measurement_time": "morning"
}
```

### Nutrition Calculator
```http
POST /api/health_tools.php/nutrition
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "age": 28,
  "gender": "female",
  "height": 165,
  "weight": 60,
  "activity_level": "moderate",
  "goal": "maintain"
}
```

## 🗄️ Database Schema

### Users Table
- `id` (Primary Key)
- `username` (Unique)
- `email` (Unique)
- `password_hash`
- `full_name`
- `due_date` (Nullable)
- `is_pregnant` (Boolean)
- `created_at`, `updated_at`

### Health Calculations Table
- `id` (Primary Key)
- `user_id` (Foreign Key)
- `calculation_type` (Enum)
- `input_data` (JSON)
- `result_data` (JSON)
- `created_at`

### Health Metrics Table
- `id` (Primary Key)
- `user_id` (Foreign Key)
- `metric_type` (Enum)
- `value`, `unit`
- `notes`
- `recorded_at`

### Appointments Table
- `id` (Primary Key)
- `user_id` (Foreign Key)
- `title`, `description`
- `appointment_date`
- `doctor_name`
- `is_completed`
- `created_at`

## 🔒 Security Features

- **Password Hashing**: Using PHP's `password_hash()` with bcrypt
- **JWT Authentication**: Secure token-based authentication
- **SQL Injection Prevention**: Using PDO prepared statements
- **Input Sanitization**: HTML special characters filtering
- **CORS Headers**: Proper cross-origin resource sharing

## 🧪 Testing

### Sample Credentials
- **Username**: `demo_user`
- **Password**: `demo123`
- **Email**: `<EMAIL>`

### Testing with cURL

```bash
# Login
curl -X POST http://your-domain/php_backend/api/auth.php/login \
  -H "Content-Type: application/json" \
  -d '{"username":"demo_user","password":"demo123"}'

# Get Dashboard (replace TOKEN with actual JWT)
curl -X GET http://your-domain/php_backend/api/dashboard.php \
  -H "Authorization: Bearer TOKEN"
```

## 🚀 Deployment

### Production Considerations
1. **Environment Variables**: Move sensitive config to environment variables
2. **HTTPS**: Enable SSL/TLS encryption
3. **Database Security**: Use strong passwords and restricted access
4. **Error Handling**: Implement proper error logging
5. **Rate Limiting**: Add API rate limiting
6. **Backup Strategy**: Regular database backups

### Apache Configuration
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/$1.php [QSA,L]
```

### Nginx Configuration
```nginx
location /api/ {
    try_files $uri $uri.php $uri/ =404;
    fastcgi_pass unix:/var/run/php/php7.4-fpm.sock;
    fastcgi_index index.php;
    include fastcgi_params;
}
```

## 📝 License

This project is part of the MATERNIFY platform and is intended for educational and development purposes.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions, please contact the development team or create an issue in the project repository.
