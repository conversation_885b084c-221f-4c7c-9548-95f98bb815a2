<?php
/**
 * Forum API for MATERNIFY PHP Backend
 */

header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

include_once '../config/database.php';
include_once '../models/User.php';
include_once '../models/ForumPost.php';
include_once '../models/ForumReply.php';

$database = new Database();
$db = $database->getConnection();

if ($db === null) {
    http_response_code(500);
    echo json_encode(["message" => "Database connection failed"]);
    exit();
}

// Verify authentication
$headers = getallheaders();
$token = null;

if (isset($headers['Authorization'])) {
    $auth_header = $headers['Authorization'];
    if (preg_match('/Bearer\s+(.*)$/i', $auth_header, $matches)) {
        $token = $matches[1];
    }
}

if (!$token) {
    http_response_code(401);
    echo json_encode(["success" => false, "message" => "Authentication required"]);
    exit();
}

$user_id = verifyToken($token);
if (!$user_id) {
    http_response_code(401);
    echo json_encode(["success" => false, "message" => "Invalid token"]);
    exit();
}

$method = $_SERVER['REQUEST_METHOD'];
$request_uri = $_SERVER['REQUEST_URI'];

// Parse the endpoint
$path_parts = explode('/', trim(parse_url($request_uri, PHP_URL_PATH), '/'));
$endpoint = end($path_parts);

switch ($method) {
    case 'GET':
        if ($endpoint === 'forum.php' || $endpoint === 'forum') {
            handleGetForumPosts($db);
        } elseif (preg_match('/^post\/(\d+)$/', $endpoint, $matches)) {
            handleGetPost($db, $matches[1]);
        } elseif (preg_match('/^post\/(\d+)\/replies$/', $endpoint, $matches)) {
            handleGetReplies($db, $matches[1]);
        } else {
            http_response_code(404);
            echo json_encode(["message" => "Endpoint not found"]);
        }
        break;
        
    case 'POST':
        $data = json_decode(file_get_contents("php://input"), true);
        
        if ($endpoint === 'create-post') {
            handleCreatePost($db, $user_id, $data);
        } elseif (preg_match('/^post\/(\d+)\/reply$/', $endpoint, $matches)) {
            handleCreateReply($db, $user_id, $matches[1], $data);
        } elseif (preg_match('/^post\/(\d+)\/like$/', $endpoint, $matches)) {
            handleLikePost($db, $matches[1]);
        } elseif (preg_match('/^reply\/(\d+)\/like$/', $endpoint, $matches)) {
            handleLikeReply($db, $matches[1]);
        } else {
            http_response_code(404);
            echo json_encode(["message" => "Endpoint not found"]);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(["message" => "Method not allowed"]);
        break;
}

function handleGetForumPosts($db) {
    try {
        $category = $_GET['category'] ?? '';
        $sort = $_GET['sort'] ?? 'recent';
        $limit = (int)($_GET['limit'] ?? 20);
        $search = $_GET['search'] ?? '';

        $forumPost = new ForumPost($db);

        if (!empty($search)) {
            $posts = $forumPost->search($search, $category, $limit);
        } else {
            $posts = $forumPost->readAll($category, $sort, $limit);
        }

        // Convert posts to proper format
        $posts_data = [];
        foreach ($posts as $post) {
            $posts_data[] = $forumPost->toArray($post);
        }

        // Get category statistics
        $category_stats = $forumPost->getCategoryStats();

        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => [
                'posts' => $posts_data,
                'category_stats' => $category_stats,
                'current_category' => $category,
                'current_sort' => $sort
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

function handleGetPost($db, $post_id) {
    try {
        $forumPost = new ForumPost($db);
        $forumPost->id = $post_id;

        $post_data = $forumPost->readOne();
        if (!$post_data) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Post not found']);
            return;
        }

        // Increment view count
        $forumPost->incrementViews();

        // Get replies
        $replies = $forumPost->getReplies();
        $forumReply = new ForumReply($db);
        $replies_data = [];
        foreach ($replies as $reply) {
            $replies_data[] = $forumReply->toArray($reply);
        }

        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => [
                'post' => $forumPost->toArray($post_data),
                'replies' => $replies_data
            ]
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

function handleGetReplies($db, $post_id) {
    try {
        $forumReply = new ForumReply($db);
        $replies = $forumReply->readByPost($post_id);

        $replies_data = [];
        foreach ($replies as $reply) {
            $replies_data[] = $forumReply->toArray($reply);
        }

        http_response_code(200);
        echo json_encode([
            'success' => true,
            'data' => $replies_data
        ]);

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

function handleCreatePost($db, $user_id, $data) {
    try {
        $title = trim($data['title'] ?? '');
        $content = trim($data['content'] ?? '');
        $category = $data['category'] ?? 'general';
        $is_anonymous = $data['is_anonymous'] ?? false;

        // Validation
        if (empty($title) || empty($content)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Title and content are required']);
            return;
        }

        $valid_categories = ['pregnancy', 'newborn', 'feeding', 'health', 'support', 'general'];
        if (!in_array($category, $valid_categories)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Invalid category']);
            return;
        }

        // Create post
        $forumPost = new ForumPost($db);
        $forumPost->user_id = $user_id;
        $forumPost->title = $title;
        $forumPost->content = $content;
        $forumPost->category = $category;
        $forumPost->is_anonymous = $is_anonymous;

        if ($forumPost->create()) {
            http_response_code(201);
            echo json_encode([
                'success' => true,
                'message' => 'Post created successfully!',
                'post_id' => $forumPost->id
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'error' => 'Failed to create post']);
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

function handleCreateReply($db, $user_id, $post_id, $data) {
    try {
        $content = trim($data['content'] ?? '');
        $is_anonymous = $data['is_anonymous'] ?? false;

        // Validation
        if (empty($content)) {
            http_response_code(400);
            echo json_encode(['success' => false, 'error' => 'Reply content is required']);
            return;
        }

        // Check if post exists
        $forumPost = new ForumPost($db);
        $forumPost->id = $post_id;
        if (!$forumPost->readOne()) {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Post not found']);
            return;
        }

        // Create reply
        $forumReply = new ForumReply($db);
        $forumReply->post_id = $post_id;
        $forumReply->user_id = $user_id;
        $forumReply->content = $content;
        $forumReply->is_anonymous = $is_anonymous;

        if ($forumReply->create()) {
            http_response_code(201);
            echo json_encode([
                'success' => true,
                'message' => 'Reply posted successfully!',
                'reply_id' => $forumReply->id
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'error' => 'Failed to create reply']);
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

function handleLikePost($db, $post_id) {
    try {
        $forumPost = new ForumPost($db);
        $forumPost->id = $post_id;

        if ($forumPost->incrementLikes()) {
            http_response_code(200);
            echo json_encode([
                'success' => true,
                'message' => 'Post liked successfully!'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'error' => 'Failed to like post']);
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

function handleLikeReply($db, $reply_id) {
    try {
        $forumReply = new ForumReply($db);
        $forumReply->id = $reply_id;

        if ($forumReply->incrementLikes()) {
            http_response_code(200);
            echo json_encode([
                'success' => true,
                'message' => 'Reply liked successfully!'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'error' => 'Failed to like reply']);
        }

    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

function verifyToken($token) {
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        return false;
    }
    
    $header = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[0]));
    $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[1]));
    $signature = $parts[2];
    
    $expected_signature = str_replace(['+', '/', '='], ['-', '_', ''], 
        base64_encode(hash_hmac('sha256', $parts[0] . "." . $parts[1], 'your-secret-key', true)));
    
    if ($signature !== $expected_signature) {
        return false;
    }
    
    $payload_data = json_decode($payload, true);
    if ($payload_data['exp'] < time()) {
        return false;
    }
    
    return $payload_data['user_id'];
}
?>
