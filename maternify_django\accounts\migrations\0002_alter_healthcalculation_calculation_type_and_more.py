# Generated by Django 5.2.1 on 2025-05-28 05:33

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='healthcalculation',
            name='calculation_type',
            field=models.CharField(choices=[('bmi', 'BMI Calculator'), ('due_date', 'Due Date Calculator'), ('weight_gain', 'Weight Gain Calculator'), ('blood_pressure', 'Blood Pressure Tracker'), ('heart_rate', 'Heart Rate Monitor'), ('nutrition', 'Nutrition Calculator'), ('water_intake', 'Water Intake Tracker'), ('sleep_quality', 'Sleep Quality Tracker')], max_length=20),
        ),
        migrations.CreateModel(
            name='HealthMetric',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('metric_type', models.CharField(choices=[('blood_pressure', 'Blood Pressure'), ('heart_rate', 'Heart Rate'), ('weight', 'Weight'), ('water_intake', 'Water Intake'), ('sleep_hours', 'Sleep Hours'), ('mood', 'Mood Rating'), ('temperature', 'Body Temperature'), ('glucose', 'Blood Glucose')], max_length=20)),
                ('value', models.FloatField()),
                ('unit', models.CharField(default='', max_length=10)),
                ('notes', models.TextField(blank=True)),
                ('recorded_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='health_metrics', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-recorded_at'],
            },
        ),
        migrations.CreateModel(
            name='MedicationReminder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('medication_name', models.CharField(max_length=100)),
                ('dosage', models.CharField(max_length=50)),
                ('frequency', models.CharField(choices=[('once_daily', 'Once Daily'), ('twice_daily', 'Twice Daily'), ('three_times_daily', 'Three Times Daily'), ('four_times_daily', 'Four Times Daily'), ('as_needed', 'As Needed'), ('weekly', 'Weekly'), ('monthly', 'Monthly')], max_length=20)),
                ('time_of_day', models.TimeField()),
                ('start_date', models.DateField()),
                ('end_date', models.DateField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='medications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['time_of_day'],
            },
        ),
        migrations.CreateModel(
            name='NutritionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('calories_consumed', models.IntegerField(default=0)),
                ('calories_target', models.IntegerField(default=2000)),
                ('water_intake', models.FloatField(default=0.0)),
                ('water_target', models.FloatField(default=2.5)),
                ('protein', models.FloatField(default=0.0)),
                ('carbs', models.FloatField(default=0.0)),
                ('fat', models.FloatField(default=0.0)),
                ('fiber', models.FloatField(default=0.0)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='nutrition_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-date'],
                'unique_together': {('user', 'date')},
            },
        ),
    ]
