from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from core.models import TeamM<PERSON>ber, Testimonial, CompanyMilestone

User = get_user_model()


class Command(BaseCommand):
    help = 'Populate sample data for About page'

    def handle(self, *args, **options):
        self.stdout.write('Populating About page data...')

        # Create team members
        team_members_data = [
            {
                'name': 'Dr. <PERSON>',
                'role': 'Chief Medical Officer',
                'specialization': 'Obstetrics & Gynecology',
                'bio': '15+ years experience in maternal healthcare. Board-certified OB/GYN with expertise in high-risk pregnancies and prenatal care.',
                'years_experience': 15,
                'order': 1
            },
            {
                'name': 'Dr. <PERSON>',
                'role': 'Pediatric Specialist',
                'specialization': 'Newborn Care & Development',
                'bio': 'Expert in infant development and care with 12+ years of experience. Specializes in newborn health and early childhood development.',
                'years_experience': 12,
                'order': 2
            },
            {
                'name': '<PERSON>',
                'role': 'Community Manager',
                'specialization': 'Maternal Support & Lactation',
                'bio': 'Certified lactation consultant and mother of three. Passionate about supporting mothers through their breastfeeding journey.',
                'years_experience': 8,
                'order': 3
            },
            {
                'name': 'Dr. <PERSON>',
                'role': 'Mental Health Specialist',
                'specialization': 'Perinatal Psychology',
                'bio': 'Licensed psychologist specializing in perinatal mental health, postpartum depression, and maternal wellness.',
                'years_experience': 10,
                'order': 4
            }
        ]

        for member_data in team_members_data:
            team_member, created = TeamMember.objects.get_or_create(
                name=member_data['name'],
                defaults=member_data
            )
            if created:
                self.stdout.write(f'Created team member: {team_member.name}')
            else:
                self.stdout.write(f'Team member already exists: {team_member.name}')

        # Create company milestones
        milestones_data = [
            {
                'year': '2023',
                'title': 'Platform Launch',
                'description': 'MATERNIFY was founded to bridge the gap in maternal healthcare support, providing evidence-based information and community support.',
                'icon': 'fas fa-rocket',
                'order': 1
            },
            {
                'year': '2023',
                'title': 'First 1000 Users',
                'description': 'Reached our first milestone of 1000 registered users within 3 months of launch.',
                'icon': 'fas fa-users',
                'order': 2
            },
            {
                'year': '2024',
                'title': 'Health Tools Launch',
                'description': 'Launched comprehensive health calculators including BMI, blood pressure tracking, and nutrition planning tools.',
                'icon': 'fas fa-calculator',
                'order': 3
            },
            {
                'year': '2024',
                'title': 'Community Growth',
                'description': 'Expanded to serve over 5000+ mothers worldwide with active community forums and expert guidance.',
                'icon': 'fas fa-globe',
                'order': 4
            },
            {
                'year': '2024',
                'title': 'Expert Partnership',
                'description': 'Partnered with leading healthcare professionals to provide verified medical content and guidance.',
                'icon': 'fas fa-handshake',
                'order': 5
            }
        ]

        for milestone_data in milestones_data:
            milestone, created = CompanyMilestone.objects.get_or_create(
                year=milestone_data['year'],
                title=milestone_data['title'],
                defaults=milestone_data
            )
            if created:
                self.stdout.write(f'Created milestone: {milestone.title}')
            else:
                self.stdout.write(f'Milestone already exists: {milestone.title}')

        # Create sample testimonials (if users exist)
        if User.objects.exists():
            testimonials_data = [
                {
                    'content': 'MATERNIFY has been an incredible resource throughout my pregnancy journey. The health calculators and community support have given me confidence and peace of mind.',
                    'rating': 5,
                    'is_featured': True,
                    'is_approved': True,
                    'user_title': 'First-time Mother',
                    'user_location': 'New York, NY'
                },
                {
                    'content': 'The expert guidance and evidence-based information on MATERNIFY helped me make informed decisions about my pregnancy and baby care. Highly recommended!',
                    'rating': 5,
                    'is_featured': True,
                    'is_approved': True,
                    'user_title': 'Mother of Two',
                    'user_location': 'Los Angeles, CA'
                },
                {
                    'content': 'I love the community aspect of MATERNIFY. Being able to connect with other mothers and share experiences has been invaluable during this journey.',
                    'rating': 5,
                    'is_featured': True,
                    'is_approved': True,
                    'user_title': 'Expecting Mother',
                    'user_location': 'Chicago, IL'
                }
            ]

            # Use the superuser or first available user for testimonials
            sample_user = User.objects.first()
            
            for i, testimonial_data in enumerate(testimonials_data):
                testimonial, created = Testimonial.objects.get_or_create(
                    user=sample_user,
                    content=testimonial_data['content'],
                    defaults=testimonial_data
                )
                if created:
                    self.stdout.write(f'Created testimonial {i+1}')
                else:
                    self.stdout.write(f'Testimonial {i+1} already exists')

        self.stdout.write(
            self.style.SUCCESS('Successfully populated About page data!')
        )
