/* Authentication Pages Styles */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2rem 1rem;
}

.auth-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    text-align: center;
}

.auth-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 1rem;
}

.auth-logo i {
    margin-right: 10px;
    color: #ff6b9d;
}

.auth-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    color: #666;
    margin-bottom: 2rem;
}

.auth-form {
    text-align: left;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: #f8f9fa;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
}

.form-input.error {
    border-color: #dc3545;
}

.form-input.success {
    border-color: #28a745;
}

.password-container {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 1.1rem;
}

.password-toggle:hover {
    color: #333;
}

.form-error {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: none;
}

.form-error.show {
    display: block;
}

.form-success {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: none;
}

.form-success.show {
    display: block;
}

.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    height: 4px;
    background: #e1e5e9;
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: width 0.3s ease, background-color 0.3s ease;
    width: 0%;
}

.strength-fill.weak {
    background: #dc3545;
    width: 33%;
}

.strength-fill.medium {
    background: #ffc107;
    width: 66%;
}

.strength-fill.strong {
    background: #28a745;
    width: 100%;
}

.strength-text {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    color: #666;
}

.auth-button {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
}

.auth-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.auth-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.auth-divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
    color: #666;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
}

.auth-divider span {
    background: white;
    padding: 0 1rem;
}

.auth-link {
    text-align: center;
    margin-top: 1.5rem;
}

.auth-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
}

.auth-link a:hover {
    text-decoration: underline;
}

.back-to-home {
    position: absolute;
    top: 2rem;
    left: 2rem;
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.back-to-home:hover {
    opacity: 0.8;
}

.back-to-home i {
    margin-right: 0.5rem;
}

/* Alert Styles */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.alert-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
}

.alert-close:hover {
    opacity: 1;
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }

    .auth-title {
        font-size: 1.5rem;
    }

    .back-to-home {
        position: static;
        margin-bottom: 2rem;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .auth-container {
        padding: 1rem;
    }

    .auth-card {
        padding: 1.5rem 1rem;
    }
}
