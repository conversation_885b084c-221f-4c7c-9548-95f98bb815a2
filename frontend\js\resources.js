// Resources JavaScript functionality

let currentResourceCategory = 'videos';

// Initialize resources page
document.addEventListener('DOMContentLoaded', function() {
    const user = getCurrentUser();
    if (user) {
        document.getElementById('userGreeting').textContent = `Hi, ${user.name || user.fullName || 'User'}!`;
    }
    
    // Initialize video filtering
    initializeVideoFiltering();
});

// Resource category navigation
function showResourceCategory(category) {
    currentResourceCategory = category;
    
    // Hide all resource sections
    document.querySelectorAll('.resource-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.resource-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(category + '-section').classList.add('active');
    
    // Add active class to clicked tab
    event.target.closest('.resource-tab').classList.add('active');
}

// Video filtering functionality
function initializeVideoFiltering() {
    const videoFilter = document.getElementById('videoFilter');
    if (videoFilter) {
        videoFilter.addEventListener('change', filterVideos);
    }
}

function filterVideos() {
    const selectedCategory = document.getElementById('videoFilter').value;
    const videoCards = document.querySelectorAll('.video-card');
    
    videoCards.forEach(card => {
        const cardCategory = card.getAttribute('data-category');
        
        if (selectedCategory === 'all' || cardCategory === selectedCategory) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Article functionality
function openArticle(articleId) {
    // In a real application, this would open the full article
    // For demo purposes, we'll show an alert
    const articles = {
        'prenatal-vitamins': {
            title: 'Understanding Prenatal Vitamins',
            content: 'Prenatal vitamins are specially formulated supplements designed to support the nutritional needs of pregnant women...'
        },
        'postpartum-depression': {
            title: 'Postpartum Depression: Signs and Support',
            content: 'Postpartum depression affects up to 15% of new mothers and is a serious but treatable condition...'
        },
        'solid-foods': {
            title: 'Introducing Solid Foods to Your Baby',
            content: 'Starting solid foods is an exciting milestone in your baby\'s development...'
        },
        'sleep-training': {
            title: 'Baby Sleep Training Methods',
            content: 'There are several approaches to help your baby develop healthy sleep patterns...'
        },
        'postpartum-exercise': {
            title: 'Postpartum Recovery and Exercise',
            content: 'Returning to exercise after childbirth should be done gradually and safely...'
        },
        'baby-proofing': {
            title: 'Baby-Proofing Your Home',
            content: 'As your baby becomes mobile, it\'s important to create a safe environment...'
        }
    };
    
    const article = articles[articleId];
    if (article) {
        showArticleModal(article);
    }
}

function showArticleModal(article) {
    // Create modal for article display
    const modal = document.createElement('div');
    modal.className = 'article-modal';
    modal.innerHTML = `
        <div class="article-modal-content">
            <div class="article-modal-header">
                <h2>${article.title}</h2>
                <button class="article-modal-close" onclick="closeArticleModal()">&times;</button>
            </div>
            <div class="article-modal-body">
                <p>${article.content}</p>
                <p><em>This is a preview of the article. In a full implementation, the complete article content would be displayed here.</em></p>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeArticleModal();
        }
    });
}

function closeArticleModal() {
    const modal = document.querySelector('.article-modal');
    if (modal) {
        modal.remove();
    }
}

// Download functionality
function downloadResource(filename) {
    // In a real application, this would trigger an actual download
    // For demo purposes, we'll show a success message
    showDownloadNotification(filename);
    
    // Track download in localStorage for demo purposes
    trackDownload(filename);
}

function showDownloadNotification(filename) {
    const notification = document.createElement('div');
    notification.className = 'download-notification';
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-check-circle"></i>
            <span>Download started: ${filename}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

function trackDownload(filename) {
    const downloads = JSON.parse(localStorage.getItem('userDownloads') || '[]');
    downloads.push({
        filename: filename,
        timestamp: new Date().toISOString()
    });
    localStorage.setItem('userDownloads', JSON.stringify(downloads));
}

// Webinar functionality
function showWebinarType(type) {
    // Hide all webinar content
    document.querySelectorAll('.webinar-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // Remove active class from all webinar tabs
    document.querySelectorAll('.webinar-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected webinar content
    document.getElementById(type + '-webinars').classList.add('active');
    
    // Add active class to clicked tab
    event.target.closest('.webinar-tab').classList.add('active');
}

// Webinar registration
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('register-btn')) {
        registerForWebinar(e.target);
    }
    
    if (e.target.classList.contains('watch-btn')) {
        watchRecordedWebinar(e.target);
    }
});

function registerForWebinar(button) {
    const webinarCard = button.closest('.webinar-card');
    const webinarTitle = webinarCard.querySelector('h3').textContent;
    
    // In a real application, this would open a registration form
    // For demo purposes, we'll show a success message
    button.innerHTML = '<i class="fas fa-check"></i> Registered';
    button.disabled = true;
    button.style.background = '#28a745';
    
    // Track registration
    trackWebinarRegistration(webinarTitle);
    
    showAlert(`Successfully registered for: ${webinarTitle}`, 'success');
}

function watchRecordedWebinar(button) {
    const webinarCard = button.closest('.webinar-card');
    const webinarTitle = webinarCard.querySelector('h3').textContent;
    
    // In a real application, this would open the video player
    // For demo purposes, we'll show a message
    showAlert(`Opening video: ${webinarTitle}`, 'info');
    
    // Track view
    trackWebinarView(webinarTitle);
}

function trackWebinarRegistration(webinarTitle) {
    const registrations = JSON.parse(localStorage.getItem('webinarRegistrations') || '[]');
    registrations.push({
        title: webinarTitle,
        timestamp: new Date().toISOString(),
        type: 'registration'
    });
    localStorage.setItem('webinarRegistrations', JSON.stringify(registrations));
}

function trackWebinarView(webinarTitle) {
    const views = JSON.parse(localStorage.getItem('webinarViews') || '[]');
    views.push({
        title: webinarTitle,
        timestamp: new Date().toISOString(),
        type: 'view'
    });
    localStorage.setItem('webinarViews', JSON.stringify(views));
}

// Search functionality (if implemented)
function searchResources(query) {
    // This would implement search across all resource types
    console.log('Searching for:', query);
}

// Bookmark functionality
function bookmarkResource(resourceId, resourceType) {
    const bookmarks = JSON.parse(localStorage.getItem('resourceBookmarks') || '[]');
    
    const bookmark = {
        id: resourceId,
        type: resourceType,
        timestamp: new Date().toISOString()
    };
    
    // Check if already bookmarked
    const existingIndex = bookmarks.findIndex(b => b.id === resourceId && b.type === resourceType);
    
    if (existingIndex === -1) {
        bookmarks.push(bookmark);
        showAlert('Resource bookmarked!', 'success');
    } else {
        bookmarks.splice(existingIndex, 1);
        showAlert('Bookmark removed!', 'info');
    }
    
    localStorage.setItem('resourceBookmarks', JSON.stringify(bookmarks));
}

// Get user's resource activity
function getUserResourceActivity() {
    return {
        downloads: JSON.parse(localStorage.getItem('userDownloads') || '[]'),
        webinarRegistrations: JSON.parse(localStorage.getItem('webinarRegistrations') || '[]'),
        webinarViews: JSON.parse(localStorage.getItem('webinarViews') || '[]'),
        bookmarks: JSON.parse(localStorage.getItem('resourceBookmarks') || '[]')
    };
}

// Export functions for global use
window.resourcesFunctions = {
    showResourceCategory,
    filterVideos,
    openArticle,
    downloadResource,
    showWebinarType,
    searchResources,
    bookmarkResource,
    getUserResourceActivity
};
