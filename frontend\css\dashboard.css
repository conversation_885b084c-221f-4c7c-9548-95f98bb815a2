/* Dashboard Styles */
.dashboard-main {
    padding-top: 100px;
    min-height: 100vh;
    background: #f8f9fa;
}

.welcome-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 3rem;
    border-radius: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.welcome-content h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.welcome-content p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.welcome-stats {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 150px;
    backdrop-filter: blur(10px);
}

.stat-card i {
    font-size: 2rem;
    color: #ff6b9d;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 3rem;
}

.quick-actions h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
}

.action-card i {
    font-size: 3rem;
    color: #ff6b9d;
    margin-bottom: 1rem;
}

.action-card h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.action-card p {
    color: #666;
    font-size: 0.95rem;
}

/* Tips Section */
.tips-section {
    margin-bottom: 3rem;
}

.tips-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.tips-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.tip-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.tip-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b9d, #e55a87);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.tip-icon i {
    font-size: 1.5rem;
    color: white;
}

.tip-content h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.tip-content p {
    color: #666;
    line-height: 1.6;
}

/* Activity Section */
.activity-section {
    margin-bottom: 3rem;
}

.activity-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.activity-list {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.activity-item {
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 50px;
    height: 50px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.activity-icon i {
    font-size: 1.2rem;
    color: #667eea;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    color: #333;
}

.activity-content p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #999;
}

/* Reminders Section */
.reminders-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #333;
}

.reminders-list {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    overflow: hidden;
}

.reminder-item {
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

.reminder-item:last-child {
    border-bottom: none;
}

.reminder-date {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b9d, #e55a87);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.reminder-date .day {
    font-size: 1.2rem;
    font-weight: bold;
    line-height: 1;
}

.reminder-date .month {
    font-size: 0.8rem;
    text-transform: uppercase;
}

.reminder-content {
    flex: 1;
}

.reminder-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
    color: #333;
}

.reminder-content p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.reminder-time {
    font-size: 0.8rem;
    color: #999;
}

.reminder-action {
    flex-shrink: 0;
}

.btn-small {
    padding: 0.5rem 1rem;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: background 0.3s ease;
}

.btn-small:hover {
    background: #5a6fd8;
}

/* User greeting in navigation */
.user-greeting {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 20px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 500 !important;
}

.logout-btn {
    background: rgba(255, 107, 157, 0.2) !important;
    border: 1px solid rgba(255, 107, 157, 0.3) !important;
    border-radius: 20px !important;
}

.logout-btn:hover {
    background: #ff6b9d !important;
    color: white !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-section {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1rem;
    }

    .welcome-content h1 {
        font-size: 2rem;
    }

    .welcome-stats {
        justify-content: center;
    }

    .stat-card {
        min-width: 120px;
        padding: 1rem;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }

    .tips-container {
        grid-template-columns: 1fr;
    }

    .tip-card {
        flex-direction: column;
        text-align: center;
    }

    .activity-item,
    .reminder-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .reminder-item {
        flex-direction: row;
        text-align: left;
    }
}

/* Tools Page Styles */
.page-header {
    text-align: center;
    margin-bottom: 3rem;
}

.page-header h1 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.tools-section {
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.tool-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

.tool-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.tool-header i {
    font-size: 2rem;
    color: #ff6b9d;
}

.tool-header h2 {
    font-size: 1.8rem;
    margin: 0;
}

.tool-content {
    padding: 2rem;
}

.tool-content > p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.tool-form {
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-input,
.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    background: #f8f9fa;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #667eea;
    background: white;
}

.input-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.input-group .form-input {
    flex: 1;
}

.input-unit {
    background: #f8f9fa;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    color: #666;
    font-weight: 500;
}

.tool-result {
    margin-top: 2rem;
}

.result-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    border-left: 5px solid #ff6b9d;
}

.result-card h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1.5rem;
    text-align: center;
}

.result-highlight {
    text-align: center;
    margin-bottom: 2rem;
}

.result-date,
.result-bmi,
.result-range {
    display: block;
    font-size: 2.5rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 0.5rem;
}

.result-category {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    margin: 0 auto;
    width: fit-content;
}

.result-category.underweight {
    background: #e3f2fd;
    color: #1976d2;
}

.result-category.normal {
    background: #e8f5e8;
    color: #388e3c;
}

.result-category.overweight {
    background: #fff3e0;
    color: #f57c00;
}

.result-category.obese {
    background: #ffebee;
    color: #d32f2f;
}

.result-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.detail-label {
    font-weight: 600;
    color: #333;
}

.detail-value {
    font-weight: bold;
    color: #667eea;
}

.bmi-chart {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.bmi-range {
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.bmi-range.underweight {
    background: #e3f2fd;
    color: #1976d2;
}

.bmi-range.normal {
    background: #e8f5e8;
    color: #388e3c;
}

.bmi-range.overweight {
    background: #fff3e0;
    color: #f57c00;
}

.bmi-range.obese {
    background: #ffebee;
    color: #d32f2f;
}

.bmi-range.active {
    transform: scale(1.05);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.range-label {
    display: block;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.range-value {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
}

.result-note {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1rem;
    color: #856404;
}

.result-note p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .tool-header {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
    }

    .tool-content {
        padding: 1.5rem;
    }

    .result-details {
        grid-template-columns: 1fr;
    }

    .bmi-chart {
        grid-template-columns: repeat(2, 1fr);
    }

    .result-date,
    .result-bmi,
    .result-range {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .dashboard-main {
        padding-top: 80px;
    }

    .welcome-section {
        margin: 1rem;
        border-radius: 15px;
    }

    .container {
        padding: 0 1rem;
    }

    .bmi-chart {
        grid-template-columns: 1fr;
    }

    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
}
