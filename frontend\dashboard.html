<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - MATERNIFY</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-baby"></i>
                <span>MATERNIFY</span>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="dashboard.html" class="nav-link active">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a href="pregnancy-care.html" class="nav-link">Pregnancy Care</a>
                </li>
                <li class="nav-item">
                    <a href="infant-care.html" class="nav-link">Infant Care</a>
                </li>
                <li class="nav-item">
                    <a href="tools.html" class="nav-link">Tools</a>
                </li>
                <li class="nav-item">
                    <a href="forum.html" class="nav-link">Community Forum</a>
                </li>
                <li class="nav-item">
                    <a href="resources.html" class="nav-link">Resources</a>
                </li>
                <li class="nav-item">
                    <span class="nav-link user-greeting" id="userGreeting">Hi, User!</span>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link logout-btn" onclick="logout()">Logout</a>
                </li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <main class="dashboard-main">
        <div class="container">
            <!-- Welcome Section -->
            <section class="welcome-section">
                <div class="welcome-content">
                    <h1 id="welcomeTitle">Welcome back!</h1>
                    <p id="welcomeSubtitle">Here's your personalized healthcare dashboard</p>
                </div>
                <div class="welcome-stats">
                    <div class="stat-card">
                        <i class="fas fa-calendar-day"></i>
                        <div class="stat-info">
                            <span class="stat-number" id="daysToGo">--</span>
                            <span class="stat-label">Days to go</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-baby"></i>
                        <div class="stat-info">
                            <span class="stat-number" id="currentWeek">--</span>
                            <span class="stat-label">Current week</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-heartbeat"></i>
                        <div class="stat-info">
                            <span class="stat-number" id="trimester">--</span>
                            <span class="stat-label">Trimester</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <h2>Quick Actions</h2>
                <div class="actions-grid">
                    <a href="tools.html#due-date" class="action-card">
                        <i class="fas fa-calculator"></i>
                        <h3>Due Date Calculator</h3>
                        <p>Calculate your estimated due date</p>
                    </a>
                    <a href="tools.html#bmi" class="action-card">
                        <i class="fas fa-weight"></i>
                        <h3>BMI Calculator</h3>
                        <p>Check your body mass index</p>
                    </a>
                    <a href="pregnancy-care.html" class="action-card">
                        <i class="fas fa-book-medical"></i>
                        <h3>Pregnancy Guide</h3>
                        <p>Trimester-wise care guidelines</p>
                    </a>
                    <a href="infant-care.html" class="action-card">
                        <i class="fas fa-baby-carriage"></i>
                        <h3>Infant Care</h3>
                        <p>Newborn care essentials</p>
                    </a>
                    <a href="forum.html" class="action-card">
                        <i class="fas fa-comments"></i>
                        <h3>Community Forum</h3>
                        <p>Connect with other mothers</p>
                    </a>
                    <a href="resources.html" class="action-card">
                        <i class="fas fa-video"></i>
                        <h3>Educational Videos</h3>
                        <p>Expert guidance and tips</p>
                    </a>
                </div>
            </section>

            <!-- Today's Tips -->
            <section class="tips-section">
                <h2>Today's Tips</h2>
                <div class="tips-container">
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-apple-alt"></i>
                        </div>
                        <div class="tip-content">
                            <h3>Nutrition Tip</h3>
                            <p id="nutritionTip">Include plenty of leafy greens in your diet for essential folate and iron.</p>
                        </div>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-dumbbell"></i>
                        </div>
                        <div class="tip-content">
                            <h3>Exercise Tip</h3>
                            <p id="exerciseTip">Take a 20-30 minute walk daily to improve circulation and reduce swelling.</p>
                        </div>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">
                            <i class="fas fa-moon"></i>
                        </div>
                        <div class="tip-content">
                            <h3>Wellness Tip</h3>
                            <p id="wellnessTip">Practice deep breathing exercises to reduce stress and promote relaxation.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recent Activity -->
            <section class="activity-section">
                <h2>Recent Activity</h2>
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="activity-content">
                            <h4>Account Created</h4>
                            <p>Welcome to MomCare! Your journey begins now.</p>
                            <span class="activity-time" id="joinDate">Just now</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="activity-content">
                            <h4>Profile Setup</h4>
                            <p>Complete your profile for personalized recommendations.</p>
                            <span class="activity-time">Pending</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Upcoming Reminders -->
            <section class="reminders-section">
                <h2>Upcoming Reminders</h2>
                <div class="reminders-list">
                    <div class="reminder-item">
                        <div class="reminder-date">
                            <span class="day" id="nextAppointmentDay">15</span>
                            <span class="month" id="nextAppointmentMonth">Mar</span>
                        </div>
                        <div class="reminder-content">
                            <h4>Prenatal Checkup</h4>
                            <p>Schedule your next prenatal appointment</p>
                            <span class="reminder-time">2:00 PM</span>
                        </div>
                        <div class="reminder-action">
                            <button class="btn-small">Schedule</button>
                        </div>
                    </div>
                    <div class="reminder-item">
                        <div class="reminder-date">
                            <span class="day">20</span>
                            <span class="month">Mar</span>
                        </div>
                        <div class="reminder-content">
                            <h4>Vitamin Refill</h4>
                            <p>Time to refill your prenatal vitamins</p>
                            <span class="reminder-time">All day</span>
                        </div>
                        <div class="reminder-action">
                            <button class="btn-small">Remind</button>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-baby"></i>
                        <span>MATERNIFY</span>
                    </div>
                    <p>Empowering mothers with knowledge and support throughout their journey.</p>
                </div>
                <div class="footer-section">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="dashboard.html">Dashboard</a></li>
                        <li><a href="pregnancy-care.html">Pregnancy Care</a></li>
                        <li><a href="infant-care.html">Infant Care</a></li>
                        <li><a href="tools.html">Tools</a></li>
                        <li><a href="forum.html">Community Forum</a></li>
                        <li><a href="resources.html">Resources</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>Contact</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 MATERNIFY. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
        });

        function initializeDashboard() {
            const user = getCurrentUser();
            if (!user) {
                window.location.href = 'login.html';
                return;
            }

            // Update user greeting
            document.getElementById('userGreeting').textContent = `Hi, ${user.name || user.fullName || 'User'}!`;
            document.getElementById('welcomeTitle').textContent = `Welcome back, ${user.name || user.fullName || 'User'}!`;

            // Calculate pregnancy stats if due date is available
            if (user.dueDate) {
                calculatePregnancyStats(user.dueDate);
            } else {
                // Show default values for non-pregnant users or those without due date
                document.getElementById('daysToGo').textContent = '--';
                document.getElementById('currentWeek').textContent = '--';
                document.getElementById('trimester').textContent = '--';
            }

            // Update join date
            const joinDate = user.registrationTime || user.loginTime;
            if (joinDate) {
                document.getElementById('joinDate').textContent = formatTimeAgo(joinDate);
            }

            // Load personalized tips
            loadPersonalizedTips();
        }

        function calculatePregnancyStats(dueDate) {
            const due = new Date(dueDate);
            const today = new Date();
            const pregnancyStart = new Date(due);
            pregnancyStart.setDate(pregnancyStart.getDate() - 280); // 40 weeks = 280 days

            const daysToGo = Math.ceil((due - today) / (1000 * 60 * 60 * 24));
            const daysSinceStart = Math.ceil((today - pregnancyStart) / (1000 * 60 * 60 * 24));
            const currentWeek = Math.floor(daysSinceStart / 7);

            let trimester;
            if (currentWeek <= 12) trimester = '1st';
            else if (currentWeek <= 27) trimester = '2nd';
            else trimester = '3rd';

            document.getElementById('daysToGo').textContent = daysToGo > 0 ? daysToGo : 'Due!';
            document.getElementById('currentWeek').textContent = currentWeek > 0 ? currentWeek : '--';
            document.getElementById('trimester').textContent = currentWeek > 0 ? trimester : '--';
        }

        function loadPersonalizedTips() {
            const tips = {
                nutrition: [
                    "Include plenty of leafy greens in your diet for essential folate and iron.",
                    "Stay hydrated by drinking at least 8-10 glasses of water daily.",
                    "Add calcium-rich foods like dairy products and fortified plant milks.",
                    "Include protein sources like lean meats, beans, and nuts in every meal."
                ],
                exercise: [
                    "Take a 20-30 minute walk daily to improve circulation and reduce swelling.",
                    "Try prenatal yoga to improve flexibility and reduce back pain.",
                    "Swimming is an excellent low-impact exercise during pregnancy.",
                    "Practice pelvic floor exercises to prepare for delivery."
                ],
                wellness: [
                    "Practice deep breathing exercises to reduce stress and promote relaxation.",
                    "Get 7-9 hours of quality sleep each night for optimal health.",
                    "Take time for self-care activities that bring you joy.",
                    "Connect with other expectant mothers for emotional support."
                ]
            };

            // Randomly select tips
            document.getElementById('nutritionTip').textContent =
                tips.nutrition[Math.floor(Math.random() * tips.nutrition.length)];
            document.getElementById('exerciseTip').textContent =
                tips.exercise[Math.floor(Math.random() * tips.exercise.length)];
            document.getElementById('wellnessTip').textContent =
                tips.wellness[Math.floor(Math.random() * tips.wellness.length)];
        }

        function formatTimeAgo(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'Just now';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
            return `${Math.floor(diffInSeconds / 86400)} days ago`;
        }


    </script>
</body>
</html>
